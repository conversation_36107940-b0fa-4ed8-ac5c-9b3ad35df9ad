package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class WorkProgramSection(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("work_program_id")
    val workProgramId: Int?,
//    @SerializedName("workshop_type_id")
//    val workshopTypeId: Int?,
//    @SerializedName("hours_count")
//    val hoursCount: Int?,
//    @SerializedName("order")
//    val order: Int?,
//    @SerializedName("created_at")
//    val createdAt: Date?,
//    @SerializedName("updated_at")
//    val updatedAt: Date?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("workProgram")
    val workProgram: WorkProgram?,
    @SerializedName("workProgramSectionDocuments")
    val workProgramSectionDocuments: List<WorkProgramSectionDocument>?
): Parcelable