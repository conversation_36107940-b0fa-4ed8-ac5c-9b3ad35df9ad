package lc.deck.rudn.entity.endowment_fund


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class EndowmentFundContact(
    @SerializedName("address")
    val address: String?,
    @SerializedName("email")
    val email: String?,
    @SerializedName("fio")
    val fio: String?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("phone")
    val phone: String?,
    @SerializedName("position_name")
    val positionName: String?
) : Parcelable