package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcReference(
    @SerializedName("id")
    val id: Int,
    @SerializedName("need_education_program")
    val needEducationProgram: <PERSON><PERSON><PERSON>,
    @SerializedName("need_year")
    val needAcademYear: <PERSON><PERSON><PERSON>,
    @SerializedName("need_admission")
    val needAdmission: <PERSON><PERSON><PERSON>,
    @SerializedName("is_paper")
    val isPaper: <PERSON><PERSON>an,
    @SerializedName("is_signature")
    val isSignature: Boolean,
    @SerializedName("need_place_of_demand")
    val needPlaceOfDemand: <PERSON>olean,
    @SerializedName("dismission")
    val isDismission: Boolean,
    @SerializedName("fio_child")
    val hasFioChild: <PERSON><PERSON><PERSON>,
    @SerializedName("child_birthday_type")
    val hasChildBirthDayType: <PERSON><PERSON><PERSON>,
    @SerializedName("child_birthday")
    val hasChildBirthDay: <PERSON><PERSON><PERSON>,
    @SerializedName("fio_latin")
    val hasFioLatin: Boolean,
    @SerializedName("need_privacy_policy")
    val needPrivacyPolicy: Boolean,
    @SerializedName("doc_type")
    val docType: Boolean,
    @SerializedName("need_invoice_problem_type")
    val needInvoiceProblemType: Boolean,
    @SerializedName("graduate_intern")
    val graduateOrIntern: Boolean,
    @SerializedName("privacy_policy_url")
    val privacyPolicyUrl: String,
    @SerializedName("need_duplicate_reason")
    val needDuplicateReason: Boolean?,
    @SerializedName("need_period")
    val needPeriod: Boolean?,
    @SerializedName("need_period_year")
    val needPeriodYear: Boolean?,
    @SerializedName("need_calendar_year")
    val needCalendarYear: Boolean?,
    @SerializedName("need_copy")
    val needCopy: Boolean?,
    @SerializedName("need_mvd")
    val needMvd: Boolean?,
    @SerializedName("need_military_document_type")
    val needMilitaryDocumentType: Boolean?,
    @SerializedName("need_stock_categories")
    val needStockCategories: Boolean?,
    @SerializedName("need_military_personnel")
    val needMilitaryPersonnel: Boolean?,
    @SerializedName("need_military_rank")
    val needMilitaryRank: Boolean?,
    @SerializedName("need_category_of_fitness")
    val needCategoryOfFitness: Boolean?,
    @SerializedName("need_military_enlistment_office")
    val needMilitaryEnlistmentOffice: Boolean?,
    @SerializedName("need_attitudes_military_service")
    val needAttitudesMilitaryService: Boolean?,
    @SerializedName("need_number_vus")
    val needNumberVus: Boolean?,
    @SerializedName("need_military_document_series")
    val needMilitaryDocumentSeries: Boolean?,
    @SerializedName("need_military_document_number")
    val needMilitaryDocumentNumber: Boolean?,
    @SerializedName("need_citizenship")
    val needCitizenship: Boolean?,
    @SerializedName("need_identity_card")
    val needIdentityCard: Boolean?,
    @SerializedName("need_identity_card_series")
    val needIdentityCardSeries: Boolean?,
    @SerializedName("need_identity_card_number")
    val needIdentityCardNumber: Boolean?,
    @SerializedName("need_identity_card_issue_date")
    val needIdentityCardIssueDate: Boolean?,
    @SerializedName("need_identity_card_issued_by")
    val needIdentityCardIssuedBy: Boolean?,
    @SerializedName("need_birthplace")
    val needBirthplace: Boolean?,
    @SerializedName("need_birthdate")
    val needBirthdate: Boolean?,
    @SerializedName("need_sex")
    val needSex: Boolean?,
    @SerializedName("need_registry")
    val needRegistry: Boolean?,
    @SerializedName("need_education_level")
    val needEducationLevel: Boolean?,
    @SerializedName("need_name_education")
    val needNameEducation: Boolean?,
    @SerializedName("need_name_education_specialty")
    val needNameEducationSpecialty: Boolean?,
    @SerializedName("military_document_type")
    val militaryDocumentType : SingleSelectionItem?,
    @SerializedName("stock_categories")
    val stockCategories : List<SingleSelectionItem?>?,
    @SerializedName("military_personnel")
    val militaryPersonnel : List<SingleSelectionItem?>?,
    @SerializedName("military_rank")
    val militaryRank : List<SingleSelectionItem?>?,
    @SerializedName("category_of_fitness")
    val categoryOfFitness : List<SingleSelectionItem?>?,
    @SerializedName("military_enlistment_office")
    val militaryEnlistmentOffice : List<SingleSelectionItem?>?,
    @SerializedName("attitudes_military_service")
    val attitudesMilitaryService : List<SingleSelectionItem?>?,
    @SerializedName("number_vus")
    val numberVus : List<SingleSelectionItem?>?,
    @SerializedName("military_document_series")
    val militaryDocumentSeries : List<SingleSelectionItem?>?,
    @SerializedName("military_document_number")
    val militaryDocumentNumber : List<SingleSelectionItem?>?,
    @SerializedName("name_education")
    val nameEducation : List<SingleSelectionItem?>?,
    @SerializedName("name_education_specialty")
    val nameEducationSpecialty : List<SingleSelectionItem?>?,
    @SerializedName("need_reason_shift_fio")
    val needReasonShiftFio: Boolean?,
    @SerializedName("need_separate_fio")
    val needSeparateFio: Boolean?,
    @SerializedName("need_separate_fio_lat")
    val needSeparateFioLat: Boolean?,
    @SerializedName("need_type_achievement")
    val needTypeAchievement: Boolean?,
    @SerializedName("need_category_achievement")
    val needCategoryAchievement: Boolean?,
    @SerializedName("need_level_achievement")
    val needLevelAchievement: Boolean?,
    @SerializedName("need_on_contract")
    val needOnContract: Boolean?,
    @SerializedName("need_reason_transfer")
    val needReasonTransfer: Boolean?,
    @SerializedName("need_currency")
    val needCurrency: Boolean?,
    @SerializedName("need_transfer_option")
    val needTransferOption: Boolean?,
    @SerializedName("need_academic_year")
    val needAcademicYear: Boolean?,
    @SerializedName("need_semestr")
    val needSemestr : Boolean?,
    @SerializedName("need_driving_program")
    val needDrivingProgram : Boolean?,
    @SerializedName("need_client_education")
    val needClientEducation : Boolean?,
    @SerializedName("need_work_place")
    val needWorkPlace : Boolean?,
    @SerializedName("need_certificate_presence")
    val needCertificatePresence : Boolean?,
    @SerializedName("need_certificate_category")
    val needCertificateCategory : Boolean?,
    @SerializedName("need_legal_representative_name")
    val needLegalRepresentativeName : Boolean?,
    @SerializedName("need_legal_representative_snils")
    val needLegalRepresentativeSnils : Boolean?,
    @SerializedName("need_legal_representative_phone")
    val needLegalRepresentativePhone : Boolean?,
    @SerializedName("need_legal_representative_email")
    val needLegalRepresentativeEmail : Boolean?,
    @SerializedName("need_legal_representative_gender")
    val needLegalRepresentativeGender : Boolean?,
    @SerializedName("need_legal_representative_birthdate")
    val needLegalRepresentativeBirthdate : Boolean?,
    @SerializedName("need_legal_representative_passport_series")
    val needLegalRepresentativePassportSeries : Boolean?,
    @SerializedName("need_legal_representative_passport_number")
    val needLegalRepresentativePassportNumber : Boolean?,
    @SerializedName("need_legal_representative_passport_issue_date")
    val needLegalRepresentativePassportIssueDate : Boolean?,
    @SerializedName("need_legal_representative_passport_issued_by")
    val needLegalRepresentativePassportIssuedBy : Boolean?,
    @SerializedName("need_legal_representative_birth_place")
    val needLegalRepresentativeBirthPlace : Boolean?,
    @SerializedName("need_legal_representative_passport_living_place")
    val needLegalRepresentativePassportLivingPlace : Boolean?,
    @SerializedName("need_legal_representative_temporal_registration_living_place")
    val needLegalRepresentativeTemporalRegistrationLivingPlace : Boolean?,
    @SerializedName("need_faculty")
    val needFaculty : Boolean?,
    @SerializedName("need_finance_type")
    val needFinanceType : Boolean?,
    @SerializedName("need_general_education_level")
    val needGeneralEducationLevel : Boolean?,
    @SerializedName("need_speciality")
    val needSpeciality : Boolean?,
    @SerializedName("need_payment_period")
    val needPaymentPeriod : Boolean?,
    @SerializedName("need_birth_city")
    val needBirthCity : Boolean?,
    @SerializedName("need_reason_budget")
    val needReasonBudget : Boolean?,
    @SerializedName("need_client_temporary_address")
    val needClientTemporaryAddress : Boolean?,
    @SerializedName("need_father_temporary_address")
    val needFatherTemporaryAddress : Boolean?,
    @SerializedName("need_mother_temporary_address")
    val needMotherTemporaryAddress : Boolean?,
    @SerializedName("need_archive_visit_reason")
    val needArchiveVisitReason : Boolean?,
    @SerializedName("need_week_pregnancy")
    val needWeekPregnancy : Boolean?,
    @SerializedName("need_date_reference")
    val needDateReference : Boolean?,
    @SerializedName("need_allow_visit")
    val needAllowVisit : Boolean?,
    @SerializedName("need_birth_certificate")
    val needBirthCertificate : Boolean?,
    @SerializedName("need_faculty_archive")
    val needFacultyArchive : Boolean?,
    @SerializedName("need_speciality_archive")
    val needSpecialityArchive : Boolean?,
    @SerializedName("need_year_archive")
    val needYearArchive : Boolean?,
    @SerializedName("need_nld_archive")
    val needNldArchive : Boolean?,
) : Parcelable