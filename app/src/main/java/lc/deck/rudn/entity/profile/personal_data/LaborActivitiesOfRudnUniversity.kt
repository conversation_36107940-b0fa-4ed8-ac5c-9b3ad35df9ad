package lc.deck.rudn.entity.profile.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LaborActivitiesOfRudnUniversity(
    @SerialName("count")
    val count: Double?,
    @SerialName("date_of_completion_of_the_employment_contract")
    val dateOfCompletionOfTheEmploymentContract: String?,
    @SerialName("date_of_dismissal")
    val dateOfDismissal: String?,
    @SerialName("date_of_receipt")
    val dateOfReceipt: String?,
    @SerialName("fio")
    val fio: String?,
    @SerialName("headquarterID")
    val headquartersId: String?,
    @SerialName("headquarters")
    val headquarters: String?,
    @SerialName("jobTitleID")
    val jobTitleId: String?,
    @SerialName("job_title")
    val jobTitle: String?,
    @SerialName("member_of_the_rectorate")
    val memberOfTheRectorate: Boolean?,
    @SerialName("member_of_the_scientific_council")
    val memberOfTheScientificCouncil: Boolean?,
    @SerialName("reception_order_date")
    val receptionOrderDate: String?,
    @SerialName("reception_order_number")
    val receptionOrderNumber: String?,
    @SerialName("schedule")
    val schedule: String?,
    @SerialName("subdivisionID")
    val subdivisionId: String?,
    @SerialName("subdivision")
    val subdivision: String?,
    @SerialName("tan_number")
    val tanNumber: String?,
    @SerialName("type_occupancy")
    val typeOccupancy: String?,
    @SerialName("works_remotely")
    val worksRemotely: Boolean?,
    @SerialName("work_phone")
    val workPhone : String?
)