package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ActivityContactPersonRudn(
    @SerializedName("id")
    val id: Int?, // 1314
    @SerializedName("fio")
    val fio: String?, // null
    @SerializedName("phone")
    val phone: String?, // null
    @SerializedName("email")
    val email: String?, // null
    @SerializedName("activity_lang_id")
    val activityLangId: Int? // 1315
): Parcelable