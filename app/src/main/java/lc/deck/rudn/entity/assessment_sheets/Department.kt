package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> Sereg<PERSON>z
 * @created : 22,сентябрь,2022
 */
@Parcelize
data class Department(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("is_selected")
    var isSelected: Boolean = false
) : Parcelable