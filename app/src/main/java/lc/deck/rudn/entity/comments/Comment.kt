package lc.deck.rudn.entity.comments


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.Person
import java.util.*

@Parcelize
data class Comment(
    @SerializedName("id")
    val id: Int,
    @SerializedName("event_id")
    val eventId: Int,
    @SerializedName("text")
    val text: String?,
    @SerializedName("from_person_id")
    val fromPersonId: Int?,
    @SerializedName("to_person_id")
    val toPersonId: Int?,
    @SerializedName("is_teacher_comment")
    val isTeacherComment: Boolean?,
    @SerializedName("is_public")
    val isPublic: Boolean?,
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("is_deleted")
    val isDeleted: Boolean?,
    @SerializedName("parent_event_comment_id")
    val parentEventCommentId: Int?,
    @SerializedName("fromPerson")
    val fromPerson: Person?,
    @SerializedName("toPerson")
    val toPerson: Person?,
    var isMyComment: Boolean,
//    @SerializedName("is_favorite")
//    val isFavorite: Boolean?,
//    @SerializedName("parentEventCommentRelations")
//    val parentEventCommentRelations: List<ParentEventCommentRelation>?,
    @SerializedName("updated_at")
    val updatedAt: Date?
) : Parcelable {
    fun isParentComment(): Boolean = parentEventCommentId == null
}