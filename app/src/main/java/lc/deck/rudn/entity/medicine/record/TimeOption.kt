package lc.deck.rudn.entity.medicine.record

import android.os.Parcelable
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.*

/**
 * Created by Seregaryz on 22.06.2022.
 */
@Serializable
data class TimeOption(
    @SerialName("date")
    @Contextual
    val date: Date?,
    @SerialName("time")
    val times: List<TimeSlot>?
)