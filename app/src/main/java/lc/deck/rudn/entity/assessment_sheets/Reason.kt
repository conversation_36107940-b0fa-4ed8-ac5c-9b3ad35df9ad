package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Reason(
    @SerializedName("certification_result")
    val certificationResult: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("is_default")
    val isDefault: Boolean?,
    @SerializedName("name")
    val name: String?
) : Parcelable