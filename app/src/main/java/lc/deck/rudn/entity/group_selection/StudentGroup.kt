package lc.deck.rudn.entity.group_selection


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class StudentGroup(
    @SerializedName("id")
    val id: Int, // 5
    @SerializedName("academ_group_id")
    val academGroupId: Int?,
//    @SerializedName("object_id")
//    val objectId: Int?, // 195723
    @SerializedName("is_from_1c")
    val isFrom1c: Boolean?,
    @SerializedName("academGroup")
    val academGroup: AcademGroup?
//    @SerializedName("faculty_id")
//    val facultyId: Any?, // null
//    @SerializedName("created_at")
//    val createdAt: Any?, // null
//    @SerializedName("updated_at")
//    val updatedAt: Any?, // null
//    @SerializedName("curriculum_id")
//    val curriculumId: Any? // null
): Parcelable