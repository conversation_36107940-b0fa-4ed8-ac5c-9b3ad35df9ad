package lc.deck.rudn.entity.multifunctional_center.preinfo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.multifunctional_center.MfcCreation

@Parcelize
data class MfcPreInfo(
    @SerializedName("service")
    val service: MfcCreation,
    @SerializedName("serviceTemplateBlocks")
    val templateBlocks: List<MfcTemplateBlocks>
) : Parcelable