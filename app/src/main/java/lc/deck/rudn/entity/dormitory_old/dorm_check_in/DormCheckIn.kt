package lc.deck.rudn.entity.dormitory_old.dorm_check_in


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DormCheckIn(
    @SerializedName("address")
    val address: String?,
    @SerializedName("documents")
    val documents: List<Document?>?,
    @SerializedName("options")
    val options: List<Option?>?,
    @SerializedName("room")
    val room: String?,
    @SerializedName("application_number")
    val applicationNumber: String?,
    @SerializedName("arrival_date")
    val arrivalDate: String?,
    @SerializedName("arrival_time")
    val arrivalTime: String?,

    ) : Parcelable