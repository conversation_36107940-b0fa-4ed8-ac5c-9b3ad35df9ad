package lc.deck.rudn.entity.fok.abonement


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Abonement(
    @SerialName("currency")
    val currency: String?,
    @SerialName("description")
    val description: String?,
    @SerialName("directions")
    val directions: List<Direction?>?,
    @SerialName("icons")
    val icons: List<Icon?>?,
    @SerialName("id")
    val id: String?,
    @SerialName("maxAmount")
    val maxAmount: Int?,
    @SerialName("minAmount")
    val minAmount: Int?,
    @SerialName("name")
    val name: String?,
    @SerialName("price")
    val price: Int?,
    @SerialName("tags")
    val tags: List<String?>?,
    @SerialName("type")
    val type: Type?
) : java.io.Serializable