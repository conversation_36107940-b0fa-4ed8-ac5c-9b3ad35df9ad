package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * Created by Seregaryz on 12.05.2022.
 */

@Parcelize
data class RefuseReasonRequestData(
    @SerializedName("statement_id")
    val statementId: Int?,
    @SerializedName("reasons_for_refusal_id")
    val refuseReason: String?,
    @SerializedName("comment")
    val comment: String? = null
): Parcelable
