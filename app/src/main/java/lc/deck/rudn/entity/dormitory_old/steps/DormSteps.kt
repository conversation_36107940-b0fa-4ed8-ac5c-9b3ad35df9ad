package lc.deck.rudn.entity.dormitory_old.steps

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DormSteps(
    @SerializedName("arrival_data")
    val arrivalData: ArrivalData?,
    @SerializedName("booking_data")
    val bookingData: BookingData?,
    @SerializedName("dorm_application_guid")
    val dormApplicationGuid: Int?,
    @SerializedName("enrollee_status")
    val enrolleeStatus: EnrolleeStatus?,
    @SerializedName("steps")
    val steps: List<Step>?
) : Parcelable