package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ReferenceBody(
    @SerializedName("count")
    val count: Int,
    @SerializedName("place_of_demand")
    val placeOfDemand: String?,
    @SerializedName("reference_id")
    val referenceId: Int
): Parcelable