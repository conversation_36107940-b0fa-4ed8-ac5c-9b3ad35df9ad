package lc.deck.rudn.entity.profile.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PersonalDataItem(
    @SerialName("academic_degree")
    val academicDegree: String?,
    @SerialName("academic_title")
    val academicTitle: String?,
    @SerialName("address_registration")
    val addressRegistration: String?,
    @SerialName("address_residence")
    val addressResidence: String?,
    @SerialName("attitude_towards_military_duty")
    val attitudeTowardsMilitaryDuty: String?,
    @SerialName("attitude_twards_military_registration")
    val attitudeTwardsMilitaryRegistration: String?,
    @SerialName("citizenship")
    val citizenship: String?,
    @SerialName("completion_of_training")
    val completionOfTraining: String?,
    @SerialName("date_of_assignment_of_academic_title")
    val dateOfAssignmentOfAcademicTitle: String?,
    @SerialName("date_of_award_of_academic_degree")
    val dateOfAwardOfAcademicDegree: String?,
    @SerialName("date_of_award_of_phd_degree")
    val dateOfAwardOfPhdDegree: String?,
    @SerialName("date_of_birth")
    val dateOfBirth: String?,
    @SerialName("date_of_issue_of_document_education")
    val dateOfIssueOfDocumentEducation: String?,
    @SerialName("diploma_academic_degree_issued_by_organization")
    val diplomaAcademicDegreeIssuedByOrganization: String?,
    @SerialName("diploma_series_number_academic_degree")
    val diplomaSeriesNumberAcademicDegree: String?,
    @SerialName("dissertation_council_of_academic_degree")
    val dissertationCouncilOfAcademicDegree: String?,
    @SerialName("document_number_education")
    val documentNumberEducation: String?,
    @SerialName("dul_date_issue")
    val dulDateIssue : String?,
    @SerialName("dul_division_code")
    val dulDivisionCode : String?,
    @SerialName("dul_document_type")
    val dulDocumentType : String?,
    @SerialName("dul_issued_by")
    val dulIssuedBy : String?,
    @SerialName("dul_number")
    val dulNumber : String?,
    @SerialName("dul_series")
    val dulSeries : String?,
    @SerialName("dul_validity")
    val dulValidity : String?,
    @SerialName("education_major")
    val educationMajor: String?,
    @SerialName("education_qualification")
    val educationQualification: String?,
    @SerialName("educational_institution")
    val educationalInstitution: String?,
    @SerialName("emails")
    val emails: List<String?>?,
    @SerialName("fio")
    val fio: String?,
    @SerialName("former_place_of_work")
    val formerPlaceOfWork: List<FormerPlaceOfWork?>?,
    @SerialName("full_data_education")
    val fullDataEducation: List<FullDataEducation?>?,
    @SerialName("graduation_education")
    val graduationEducation: String?,
    @SerialName("home_phone")
    val homePhone: String?,
    @SerialName("inn")
    val inn: String?,
    @SerialName("labor_activities_of_rudn_university")
    val laborActivitiesOfRudnUniversity: List<LaborActivitiesOfRudnUniversity?>?,
    @SerialName("member_of_the_state_academy")
    val memberOfTheStateAcademy: Boolean?,
    @SerialName("member_of_the_trade_union")
    val memberOfTheTradeUnion: Boolean?,
    @SerialName("trade_union_join_date")
    val tradeUnionJoinDate: String?,
    @SerialName("military_registration_enlistment_office")
    val militaryRegistrationEnlistmentOffice: String?,
    @SerialName("mobile_phone")
    val mobilePhone: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("parent_with_many_children")
    val parentWithManyChildren: Boolean?,
    @SerialName("patronymic")
    val patronymic: String?,
    @SerialName("person_guid")
    val personGuid: String?,
    @SerialName("phone")
    val phone: String?,
    @SerialName("professional_contributions_are_withheld")
    val professionalContributionsAreWithheld: Boolean?,
    @SerialName("rank")
    val rank: String?,
    @SerialName("series_document_education")
    val seriesDocumentEducation: String?,
    @SerialName("sex")
    val sex: String?,
    @SerialName("snils")
    val snils: String?,
    @SerialName("start_date")
    val startDate: String?,
    @SerialName("stock_category")
    val stockCategory: String?,
    @SerialName("suitability")
    val suitability: String?,
    @SerialName("surname")
    val surname: String?,
    @SerialName("type_degree_phd")
    val typeDegreePhd: String?,
    @SerialName("type_document_education")
    val typeDocumentEducation: String?,
    @SerialName("type_education")
    val typeEducation: String?,
    @SerialName("veteran_rudn")
    val veteranRudn: Boolean?,
    @SerialName("vus")
    val vus: String?,
    @SerialName("work_books_view_applications")
    val workBooksViewApplications: String?
)