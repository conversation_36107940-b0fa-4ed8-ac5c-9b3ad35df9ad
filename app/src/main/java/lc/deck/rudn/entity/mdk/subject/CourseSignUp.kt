package lc.deck.rudn.entity.mdk.subject

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CourseSignUp(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("language_implementation_guid")
    val languageImplementationGuid: String?,
    @SerializedName("study_period_id")
    val studyPeriodId: String?,
    @SerializedName("academ_group_id")
    val academGroupId: Int?
) : Parcelable