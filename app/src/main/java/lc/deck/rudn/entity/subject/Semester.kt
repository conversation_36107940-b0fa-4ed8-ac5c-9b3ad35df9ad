package lc.deck.rudn.entity.subject

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Semester(

	@SerializedName("updated_at")
	val updatedAt: String? = null,

	@SerializedName("start")
	val start: String? = null,

	@SerializedName("created_at")
	val createdAt: String? = null,

	@SerializedName("end")
	val end: String? = null,

	@SerializedName("id")
	val id: Int? = null,

	@SerializedName("value")
	val value: String? = null
) : Parcelable
