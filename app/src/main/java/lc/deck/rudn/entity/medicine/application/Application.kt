package lc.deck.rudn.entity.medicine.application

import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
data class Application(
    @SerialName("application_guid")
    val applicationGuid: String,
    @SerialName("plan_date")
    @Contextual
    val planDate: Date? = null,
    @SerialName("plan_time")
    val planTime: String? = null,
    @SerialName("service")
    val service: Service,
    @SerialName("state")
    val state: State
)