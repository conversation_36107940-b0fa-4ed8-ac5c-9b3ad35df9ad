package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Debt(

	@SerializedName("sum_total")
	val sumTotal: Int? = null,

	@SerializedName("fine_total")
	val fineTotal: Int? = null,

	@SerializedName("guid")
	val guid: String? = null,

	@SerializedName("sum")
	val sum: Int? = null,

	@SerializedName("currency")
	val currency: String? = null,

    @SerializedName("debt_periods")
    val debtPeriods: List<DebtPeriod?>? = null
) : Parcelable
