package lc.deck.rudn.entity.personnel_transactions.agreement


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Agreement(
    @SerializedName("format")
    val format: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("size")
    val size: String?,
   /* @SerializedName("signature")
    val signature: Signature?,*/
    @SerializedName("task_guid")
    val taskGuid: String?
) : Parcelable