package lc.deck.rudn.entity.comments

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CreateCommentBody(
    @SerializedName("event_id") val eventId: Int,
    @SerializedName("text") val text: String,
    @SerializedName("is_teacher_comment") val isTeacherComment: Boolean,
    @SerializedName("is_public") val isPublic: Boolean
): Parcelable