package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentDate(
    @SerializedName("allowedPaymentNumbers")
    val allowedPaymentNumbers: List<Int?>?,
    @SerializedName("date")
    val date: String?
) : Parcelable {
    companion object {
        const val PAYMENT_DATE_1 = 1
        const val PAYMENT_DATE_2 = 2
        const val PAYMENT_DATE_3 = 3
        const val PAYMENT_DATE_4 = 4
        const val PAYMENT_DATE_5 = 5
    }
}
