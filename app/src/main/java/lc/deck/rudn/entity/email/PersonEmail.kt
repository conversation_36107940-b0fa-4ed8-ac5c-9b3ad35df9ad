package lc.deck.rudn.entity.email


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PersonEmail(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("person_id")
    val personId: Int?,
    @SerializedName("email")
    val email: String?,
    @SerializedName("is_for_connection")
    val isForConnection: Boolean?,
    @SerializedName("id_deleted")
    val idDeleted: Boolean?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("updated_at")
    val updatedAt: String?
): Parcelable