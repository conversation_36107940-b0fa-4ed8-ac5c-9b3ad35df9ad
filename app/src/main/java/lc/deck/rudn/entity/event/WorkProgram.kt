package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class WorkProgram(
//    @SerializedName("id")
//    val id: Int?,
//    @SerializedName("subject_id")
//    val subjectId: Int?,
//    @SerializedName("department_id")
//    val departmentId: Int?,
//    @SerializedName("parent_workshop_type_id")
//    val parentWorkshopTypeId: Int?,
//    @SerializedName("total_hour")
//    val totalHour: Int?,
//    @SerializedName("created_at")
//    val createdAt: Date?,
//    @SerializedName("updated_at")
//    val updatedAt: Date?,
    @SerializedName("subject")
    val subject: Subject?,
    @SerializedName("parentWorkshopType")
    val workshopTypeParent: WorkshopType?
): Parcelable