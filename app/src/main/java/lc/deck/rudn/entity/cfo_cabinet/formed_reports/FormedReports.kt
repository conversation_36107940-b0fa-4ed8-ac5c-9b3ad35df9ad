package lc.deck.rudn.entity.cfo_cabinet.formed_reports


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FormedReports(
    @SerializedName("date_create")
    val dateCreate: String?,
    @SerializedName("error_message")
    val errorMessage: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("report_date_from")
    val reportDateFrom: String?,
    @SerializedName("report_date_to")
    val reportDateTo: String?,
    @SerializedName("report_name")
    val reportName: String?,
    @SerializedName("state")
    val state: String?,
    @SerializedName("cfo_list")
    val cfoList: List<CfoList>?
) : Parcelable