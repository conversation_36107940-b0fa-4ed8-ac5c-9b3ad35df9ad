package lc.deck.rudn.entity.medicine.application.detail


import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.medicine.application.Service
import lc.deck.rudn.entity.medicine.application.State
import java.util.*

@Serializable
data class ApplicationDetail(
    @SerialName("account_number")
    val accountNumber: String,
    @SerialName("address")
    val address: String? = null,
    @SerialName("application_guid")
    val applicationGuid: String,
    @SerialName("cost")
    val cost: Cost?,
    @SerialName("documents")
    val documents: List<String>? = null,
    @SerialName("is_cancellation_allowed")
    val isCancellationAllowed: Boolean,
    @SerialName("is_need_to_pay")
    val isNeedToPay: Boolean,
    @SerialName("is_reschedule_allowed")
    val isRescheduleAllowed: <PERSON><PERSON><PERSON>,
    @SerialName("place")
    val place: String? = null,
    @SerialName("plan_date")
    @Contextual
    val planDate: Date? = null,
    @SerialName("plan_time")
    val planTime: String? = null,
    @SerialName("service")
    val service: Service,
    @SerialName("state")
    val state: State,
    @SerialName("ticket_number")
    val ticketNumber: String? = null
)