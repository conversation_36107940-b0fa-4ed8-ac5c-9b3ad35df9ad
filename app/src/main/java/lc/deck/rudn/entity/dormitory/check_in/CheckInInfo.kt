package lc.deck.rudn.entity.dormitory.check_in


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CheckInInfo(
    @SerialName("address")
    val address: String?,
    @SerialName("application_number")
    val applicationNumber: String?,
    @SerialName("arrival_date")
    val arrivalDate: String? = null,
    @SerialName("arrival_time")
    val arrivalTime: String? = null,
    @SerialName("documents")
    val documents: List<String?>? = null,
    @SerialName("room")
    val room: String?
)