package lc.deck.rudn.entity.personnel_transactions


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DocumentConfirmation(
    @SerializedName("comment")
    val comment: String?,
    @SerializedName("is_confirm")
    val isConfirm: Boolean?,
    @SerializedName("task_guid")
    val taskGuid: String?
) : Parcelable