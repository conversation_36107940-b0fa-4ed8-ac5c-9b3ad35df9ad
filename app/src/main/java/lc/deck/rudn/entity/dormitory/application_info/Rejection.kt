package lc.deck.rudn.entity.dormitory.application_info

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.dormitory.dorm_applied.File

@Serializable
data class Rejection(
    @SerialName("date_create")
    val dateCreate: String?,
    @SerialName("rejection_text")
    val rejectionText: String?,
    @SerialName("file")
    val file: File? = null
)
