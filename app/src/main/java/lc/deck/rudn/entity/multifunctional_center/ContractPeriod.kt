package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractPeriod(
    @SerializedName("class")
    val classX: String?,
    @SerializedName("currency")
    val currency: Currency?,
    @SerializedName("days_left")
    val daysLeft: Int?,
    @SerializedName("is_debt")
    val isDebt: Boolean?,
    @SerializedName("is_list")
    val isList: Boolean?,
    @SerializedName("is_reminder")
    val isReminder: Boolean?,
    @SerializedName("nomenclature")
    val nomenclature: String?,
    @SerializedName("paid_desc")
    val paidDesc: PaidDesc?,
    @SerializedName("plan_payment_date")
    val planPaymentDate: String?,
    @SerializedName("sum")
    val sum: Number?,
    @SerializedName("year")
    val year: String?
) : Parcelable