package lc.deck.rudn.entity.dormitory_old.dorm_application


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DormApplication(
    @SerializedName("dorm_application_guid")
    val dormApplicationGuid: String?,
    @SerializedName("education_level")
    val educationLevel: String?,
    @SerializedName("fact_address")
    val factAddress: String?,
    @SerializedName("faculty")
    val faculty: String?,
    @SerializedName("file")
    val `file`: File?,
    @SerializedName("fio")
    val fio: String?,
    @SerializedName("organization")
    val organization: Organization?,
    @SerializedName("registration_address")
    val registrationAddress: String?,
    @SerializedName("representatives")
    val representatives: List<Representative>?,
    @SerializedName("school")
    val school: String?,
    @SerializedName("score_ege")
    val scoreEge: String?
):Parcelable