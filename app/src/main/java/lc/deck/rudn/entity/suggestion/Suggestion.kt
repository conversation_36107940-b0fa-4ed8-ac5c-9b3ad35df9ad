package lc.deck.rudn.entity.suggestion

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Suggestion(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("user_id")
	val userId: Int?,
    @SerializedName("person_email_id")
	val personEmailId: Int?,
    @SerializedName("topic_id")
	val topicId: Int?,
    @SerializedName("text")
	val text: String?,
    @SerializedName("hash")
	val hash: String?
): Parcelable

