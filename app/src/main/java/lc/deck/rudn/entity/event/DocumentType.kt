package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DocumentType(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String?
): Parcelable {
    companion object {
        const val ARTICLE = 1
        const val TESTING = 2
        const val MEDIA = 3
    }
}