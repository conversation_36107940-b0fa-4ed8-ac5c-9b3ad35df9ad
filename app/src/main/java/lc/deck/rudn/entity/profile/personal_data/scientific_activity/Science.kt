package lc.deck.rudn.entity.profile.personal_data.scientific_activity


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Science(
    @SerialName("dateOfBirth")
    val dateOfBirth: String?,
    @SerialName("editorialBoardParticipation")
    val editorialBoardParticipation: List<EditorialBoardParticipation?>?,
    @SerialName("fio")
    val fio: String?,
    @SerialName("IDRSCI")
    val idrsci: String?,
    @SerialName("journalPublications")
    val journalPublications: List<JournalPublication?>?,
    @SerialName("personGUIDBit")
    val personGUIDBit: String?,
    @SerialName("projects")
    val projects: List<Projects?>?,
    @SerialName("RIA")
    val ria: List<Ria?>?,
    @SerialName("scopusAuthorID")
    val scopusAuthorID: String?,
    @SerialName("sex")
    val sex: String?,
    @SerialName("stateAcademyMember")
    val stateAcademyMember: Boolean?,
    @SerialName("stateAcademyRank")
    val stateAcademyRank: String?,
    @SerialName("stateAcademyYears")
    val stateAcademyYears: Int?,
    @SerialName("worldOfScienceResearchID")
    val worldOfScienceResearchID: String?
)