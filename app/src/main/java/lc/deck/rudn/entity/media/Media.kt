package lc.deck.rudn.entity.media

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.File

@Parcelize
data class Media(
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("url")
    val url: String? = null,
    @SerializedName("file_id")
    val fileId: Int? = null,
    @SerializedName("media_type_id")
    val mediaTypeId: Int? = null,
    @SerializedName("date")
    val date: String? = null,
    @SerializedName("is_default")
    val isDefault: Boolean? = null,
    @SerializedName("user_id")
    val userId: Int? = null,
    @SerializedName("created_at")
    val updatedAt: String? = null,
    @SerializedName("updated_at")
    val createdAt: String? = null,
    @SerializedName("file")
    val file: File? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("mediaType")
    val mediaType: MediaType? = null
): Parcelable