package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AcademGroup(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("is_noted")
    val isNoted: Boolean?
//    @SerializedName("faculty_id")
//    val facultyId: Int?,
//    @SerializedName("created_at")
//    val createdAt: Date?,
//    @SerializedName("updated_at")
//    val updatedAt: Date?,
//    @SerializedName("object_id")
//    val objectId: Any?
): Parcelable