package lc.deck.rudn.entity.payment.invoice


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Invoice(
    @SerializedName("accountPeriods")
    val accountPeriods: List<AccountPeriod>?,
    @SerializedName("closure_date")
    val closureDate: String?,
    @SerializedName("currency")
    val currency: CurrencyX?,
    @SerializedName("issue_date")
    val issueDate: String?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("sum")
    val sum: Double?
) : Parcelable