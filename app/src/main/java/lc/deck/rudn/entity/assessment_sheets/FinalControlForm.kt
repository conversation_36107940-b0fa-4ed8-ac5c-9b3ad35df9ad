package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FinalControlForm(
    @SerializedName("finalControlFormResult")
    val finalControlFormResult: FinalControlFormResult?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
) : Parcelable