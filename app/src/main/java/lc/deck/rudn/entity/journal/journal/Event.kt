package lc.deck.rudn.entity.journal.journal


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Event(
    @SerialName("absentStudents")
    val absentStudents: Int?,
    @SerialName("address")
    val address: Address?,
    @SerialName("attendStudents")
    val attendStudents: Int?,
    @SerialName("audience")
    val audience: Audience?,
    @SerialName("controlForm")
    val controlForm: ControlForm?,
    @SerialName("controlFormTheme")
    val controlFormTheme: ControlFormTheme?,
    @SerialName("date")
    val date: String?,
    @SerialName("eventType")
    val eventType: EventType?,
    @SerialName("id")
    val id: String?,
    @SerialName("journalNeedFilling")
    val journalNeedFilling: Boolean?,
    @SerialName("name")
    val name: String?,
    @SerialName("students")
    val students: List<Student>?,
    @SerialName("theme")
    val theme: Theme?,
    @SerialName("time")
    val time: Time?
)