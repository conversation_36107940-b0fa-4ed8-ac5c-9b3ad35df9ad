package lc.deck.rudn.entity.medicine.application


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class State(
    @SerialName("id")
    val id: Int,
    @SerialName("name")
    val name: String
) {
    companion object {
        const val STATE_1 = 1
        const val STATE_2 = 2
        const val STATE_3 = 3
        const val STATE_4 = 4
    }
}