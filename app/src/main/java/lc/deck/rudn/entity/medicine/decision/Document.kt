package lc.deck.rudn.entity.medicine.decision


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Document(
    @SerialName("comment")
    val comment: String? = null,
    @SerialName("guid")
    val guid: String,
    @SerialName("is_repeat_allowed")
    val isRepeatAllowed: <PERSON><PERSON><PERSON>,
    @SerialName("name")
    val name: String,

    @SerialName("is_enabled")
    val isEnabled: Boolean = true
)