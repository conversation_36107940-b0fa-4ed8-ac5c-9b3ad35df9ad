package lc.deck.rudn.entity.medicine.service

import lc.deck.rudn.entity.medicine.text.Rules

data class Service(
    val address: String?,
    val currency: String?,
    val description: String?,
//    val documents: List<Document>?,
    val files: List<File>?,
    val guid: String?,
    val is_agreement: Boolean?,
    val is_payment: Boolean?,
    val name: String?,
    val option: Option?,
    val price: String?,
    val room: String?,
    val rules: Rules? = null
)