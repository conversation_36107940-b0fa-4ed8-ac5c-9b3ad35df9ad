package lc.deck.rudn.entity.iep.iep


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class IndividualEduPlan(
    @SerialName("activeAgreementButton")
    val activeAgreementButton: Boolean?,
    @SerialName("agreementDeadlineDate")
    val agreementDeadlineDate: String?,
    @SerialName("course")
    val course: Int?,
    @SerialName("direction")
    val direction: Direction?,
    @SerialName("id")
    val id: String? = null,
    @SerialName("semesters")
    val semesters: List<Semester>?,
    @SerialName("speciality")
    val speciality: Speciality?,
    @SerialName("status")
    val status: Status?,
    @SerialName("studyForm")
    val studyForm: StudyForm?,
    @SerialName("studyLevel")
    val studyLevel: StudyLevel?
)