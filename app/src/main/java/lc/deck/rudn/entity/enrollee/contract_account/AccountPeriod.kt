package lc.deck.rudn.entity.enrollee.contract_account


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AccountPeriod(
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("date_end")
    val dateEnd: String?,
    @SerializedName("date_start")
    val dateStart: String?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("period_data")
    val periodData: String?,
    @SerializedName("sum")
    val sum: Double?,
    @SerializedName("description")
    val description: String?
) : Parcelable