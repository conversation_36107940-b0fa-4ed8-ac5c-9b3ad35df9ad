package lc.deck.rudn.entity.dormitory.contract

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AccountPeriod(
    @SerialName("currency")
    val currency: String?,
    @SerialName("date_end")
    val dateEnd: String?,
    @SerialName("date_start")
    val dateStart: String?,
    @SerialName("period_data")
    val periodData: String?,
    @SerialName("sum")
    val sum: Double?,
    @SerialName("description")
    val description: String?
)
