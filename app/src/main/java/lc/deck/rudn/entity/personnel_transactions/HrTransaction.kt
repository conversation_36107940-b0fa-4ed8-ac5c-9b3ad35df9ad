package lc.deck.rudn.entity.personnel_transactions


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class HrTransaction(
    @SerializedName("action")
    val action: Action?,
    @SerializedName("buttons")
    val buttons: List<Button>?,
    @SerializedName("comment")
    val comment: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("documents")
    val documents: List<Document>?,
    @SerializedName("group_guid")
    val groupGuid: String?,
    @SerializedName("is_allowed_to_sign")
    val isAllowedToSign: Boolean?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("publication_date")
    val publicationDate: String?,
    @SerializedName("sign_date")
    val signDate: String?,
    @SerializedName("sign_till_date")
    val signTillDate: String?,
    @SerializedName("state")
    val state: StateX?,
    @SerializedName("status")
    val status: Status?,
    @SerializedName("task_guid")
    val taskGuid: String?,
    @SerializedName("files")
    val file: List<File>?,
    @SerializedName("non_qualified_signature")
    val nonQualifiedSignature: Boolean?
) : Parcelable