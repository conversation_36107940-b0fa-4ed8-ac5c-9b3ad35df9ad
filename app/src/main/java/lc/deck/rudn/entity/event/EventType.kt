package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class EventType(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
): Parcelable {
    companion object {
        const val EVENT_TYPE_LESSON = 1
        const val EVENT_TYPE_ACTION = 2
        const val EVENT_TYPE_MFC = 3
    }
}