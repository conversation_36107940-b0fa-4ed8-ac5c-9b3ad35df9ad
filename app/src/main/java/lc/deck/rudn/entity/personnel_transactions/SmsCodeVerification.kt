package lc.deck.rudn.entity.personnel_transactions

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 04.07.2022, понедельник
 **/
@Parcelize
data class SmsCodeVerification(
    @SerializedName("is_confirm")
    val isConfirm: Boolean?,
    @SerializedName("pin")
    val pin: String?,
    @SerializedName("task_guid")
    val taskGuid: String?,
    @SerializedName("Request_id")
    val requestId: String?,
):Parcelable

@Parcelize
data class SmsCodePin(
    @SerializedName("pin")
    val pin: String?,
):Parcelable
