package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Application(
    @SerializedName("id")
    val id: Int,
    @SerializedName("need_return_reason")
    val needReturnReason: Boolean?,
    @SerializedName("need_dormitory")
    val needDormitory: Boolean?,
    @SerializedName("need_requisites")
    val needRequisites: Boolean?,
    @SerializedName("need_free_input_field")
    val needFreeInputField: Boolean?,
    @SerializedName("need_discount_reason")
    val needDiscountReason: Boolean?,
    @SerializedName("need_payment_option")
    val needPaymentOption: Boolean?,
    @SerializedName("need_education_programm_type")
    val needEducationProgrammType: Boolean?,
    @SerializedName("need_education_programm_benefits")
    val needEducationProgrammBenefits: Boolean?,
    @SerializedName("need_date_agenda")
    val needDateAgenda : Boolean?,
) : Parcelable
