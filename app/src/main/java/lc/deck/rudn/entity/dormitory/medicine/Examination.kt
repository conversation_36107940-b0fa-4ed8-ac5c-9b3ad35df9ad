package lc.deck.rudn.entity.dormitory.medicine


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Examination(
    @SerialName("attachments")
    var attachments: List<Attachment>?,
    @SerialName("description")
    val description: String?,
    @SerialName("id")
    val id: String?,
    @SerialName("isAttachmentsAvailable")
    val isAttachmentsAvailable: Boolean?,
    @SerialName("name")
    val name: String?,
    @SerialName("price")
    val price: Price?,
    @SerialName("status")
    val status: StatusX?,
    var goToKdc: Boolean = true
)