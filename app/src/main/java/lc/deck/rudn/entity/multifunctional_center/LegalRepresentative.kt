package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Parcelize
data class LegalRepresentative(
    @SerializedName("birthdate")
    val birthdate: String? = null,
    @SerializedName("birthplace")
    val birthplace: String? = null,
    @SerializedName("email")
    val email: String? = null,
    @SerializedName("identity_card_issue_date")
    val identityCardIssueDate: String? = null,
    @SerializedName("identity_card_issued_by")
    val identityCardIssuedBy: String? = null,
    @SerializedName("identity_card_living_place")
    val identityCardLivingPlace: String? = null,
    @SerializedName("identity_card_number")
    val identityCardNumber: String? = null,
    @SerializedName("identity_card_series")
    val identityCardSeries: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("phone_number")
    val phoneNumber: String? = null,
    @SerializedName("sex_id")
    val sexId: Int? = null,
    @SerializedName("snils")
    val snils: String? = null,
    @SerializedName("temporal_registration_living_place")
    val temporalRegistrationLivingPlace: String? = null
) : Parcelable