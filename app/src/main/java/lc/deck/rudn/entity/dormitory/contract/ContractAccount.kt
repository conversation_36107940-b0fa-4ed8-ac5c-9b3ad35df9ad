package lc.deck.rudn.entity.dormitory.contract

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ContractAccount(
    @SerialName("account_number")
    val accountNumber: String?,
    @SerialName("accountPeriods")
    val accountPeriods: List<AccountPeriod?>?,
    @SerialName("closure_date")
    val closureDate: String?,
    @SerialName("contract_issue_date")
    val contractIssueDate: String?,
    @SerialName("contract_number")
    val contractNumber: String?,
    @SerialName("currency")
    val currency: String?,
    @SerialName("description")
    val description: List<Description?>?,
    @SerialName("issue_date")
    val issueDate: String?,
    @SerialName("notification")
    val notification: String?,
    @SerialName("sum_total")
    val sumTotal: Double?
)
