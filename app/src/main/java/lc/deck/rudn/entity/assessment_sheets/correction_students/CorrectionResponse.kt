package lc.deck.rudn.entity.assessment_sheets.correction_students


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.assessment_sheets.requests.correction.CorrectionStudents

@Parcelize
data class CorrectionResponse(
    @SerializedName("correction_id")
    val correctionId: Int?,
    @SerializedName("correctionStudents")
    val correctionStudents: List<CorrectionStudents>?,
    @SerializedName("total_students")
    val totalStudents: Int?
) : Parcelable