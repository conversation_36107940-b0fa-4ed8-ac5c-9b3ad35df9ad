package lc.deck.rudn.entity.employee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.*

@Parcelize
data class EmployeePosition(
    @SerializedName("date_dismissal")
    val dateDismissal: Date?,
    @SerializedName("date_edit")
    val dateEdit: Date?,
    @SerializedName("date_received")
    val dateReceived: String?,
    @SerializedName("department")
    val department: Department?,
    @SerializedName("department_id")
    val departmentId: Int?,
    @SerializedName("employee_id")
    val employeeId: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("position")
    val position: Position?,
    @SerializedName("position_id")
    val positionId: Int?,
    @SerializedName("rate")
    val rate: Double?,
) : Parcelable