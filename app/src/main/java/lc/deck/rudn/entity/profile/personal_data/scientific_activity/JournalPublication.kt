package lc.deck.rudn.entity.profile.personal_data.scientific_activity


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class JournalPublication(
    @SerialName("authors")
    val authors: String?,
    @SerialName("citationBase")
    val citationBase: List<String?>?,
    @SerialName("edition")
    val edition: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("type")
    val type: String?,
    @SerialName("work")
    val work: String?,
    @SerialName("year")
    val year: Int?
)