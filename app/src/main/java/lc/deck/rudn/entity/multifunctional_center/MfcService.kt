package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcService(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("ready_time_day")
    val ready_time_day: String?,
    @SerializedName("price")
    val price: String?,
    @SerializedName("serviceDocuments")
    val serviceDocuments: List<MfcServiceDocument?>,
    @SerializedName("reference")
    val reference: MfcReference?,
    @SerializedName("ready_time")
    val readyTime: Int?,
    @SerializedName("is_calendar_ready_time")
    val isCalendarReadyTime: Boolean?,
    @SerializedName("date")
    val date: String?,
    @SerializedName("alias")
    val alias: String?,
) : Parcelable {

    companion object {
        const val ARG_MAKE_DAYS = 1440
    }
}