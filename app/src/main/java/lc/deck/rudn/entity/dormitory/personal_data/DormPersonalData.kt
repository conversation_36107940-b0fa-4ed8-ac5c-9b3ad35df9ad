package lc.deck.rudn.entity.dormitory.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DormPersonalData(
    @SerialName("guid")
    val guid: String?,
    @SerialName("personalData")
    val personalData: PersonalData?,
    @SerialName("documents")
    val documents: Documents?,
    @SerialName("legalRepresentative")
    val legalRepresentative: LegalRepresentative?,
)
