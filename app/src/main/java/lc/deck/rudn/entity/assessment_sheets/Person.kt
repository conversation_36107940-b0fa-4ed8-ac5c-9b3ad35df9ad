package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.employee.Employee

@Parcelize
data class Person(
    @SerializedName("file")
    val `file`: File?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name_rus")
    val nameRus: String?,
    @SerializedName("patronymic_rus")
    val patronymicRus: String?,
    @SerializedName("surname_rus")
    val surnameRus: String?,
    @SerializedName("users")
    val teacherUsers: List<TeacherUser?>?
) : Parcelable