package lc.deck.rudn.entity.enrollee.payment_schedule


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentSchedule(
    @SerializedName("contract_sum")
    val contractSum: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("paymentTypes")
    val paymentTypes: List<PaymentType?>?
) : Parcelable