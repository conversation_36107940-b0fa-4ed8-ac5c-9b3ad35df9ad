package lc.deck.rudn.entity.employee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class File(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("is_public")
    val isPublic: Boolean?,
    @SerializedName("path")
    val path: String?,
    @SerializedName("user_id")
    val userId: Int?
) : Parcelable