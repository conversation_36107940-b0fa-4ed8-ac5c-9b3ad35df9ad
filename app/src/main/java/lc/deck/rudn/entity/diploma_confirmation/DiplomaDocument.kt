package lc.deck.rudn.entity.diploma_confirmation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DiplomaDocument(
    @SerializedName("confirmationDate")
    val confirmationDate: String? = null,
    @SerializedName("confirmationDeadlineDate")
    val confirmationDeadlineDate: String? = null,
    @SerializedName("creationDate")
    val creationDate: String? = null,
    @SerializedName("diplomaGUID")
    val diplomaGUID: String? = null,
    @SerializedName("diplomaName")
    val diplomaName: String? = null,
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("status")
    val status: DiplomaStatus? = null,
) : Parcelable
