package lc.deck.rudn.entity.applicant.department


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DepartmentInfo(
    @SerializedName("address")
    val address: String?,
    @SerializedName("documents")
    val documents: List<Document>?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("workHours")
    val workHours: List<WorkHour>?
) : Parcelable