package lc.deck.rudn.entity.payment.bank


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Bank(
    @SerializedName("logo_pdf")
    val logoPdf: String,
    @SerializedName("logo_svg")
    val logoSvg: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("url")
    val url: String
) : Parcelable