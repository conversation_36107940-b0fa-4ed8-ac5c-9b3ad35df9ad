package lc.deck.rudn.entity.comments

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CommentTree(
    @SerializedName("parent_comment_id")
    val parentCommentId: Int,
    @SerializedName("child_count")
    val childCount: Int?,
    @SerializedName("parent_comment")
    val parentComment: Comment?,
    @SerializedName("last_child_comment")
    val lastChildComment: Comment?
): Parcelable