package lc.deck.rudn.entity.medicine.medicine_service.detail

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.medicine.medicine_service.Cost

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 21.06.2022, вторник
 **/
@Serializable
data class MedicineServiceDetail(
    @SerialName("address")
    val address: String? = null,
    @SerialName("cost")
    val cost: Cost? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("documents")
    val documents: List<String>? = null,
    @SerialName("guid")
    val guid: String,
    @SerialName("is_attach")
    val isAttach: <PERSON><PERSON><PERSON>,
    @SerialName("is_record")
    val isRecord: Boolean,
    @SerialName("attachments")
    val attachments: List<String>? = null,
    @SerialName("medicalTests")
    val medicalTests: List<String>? = null,
    @SerialName("name")
    val name: String,
    @SerialName("place")
    val place: String? = null
)
