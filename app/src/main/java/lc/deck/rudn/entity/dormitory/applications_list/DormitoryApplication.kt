package lc.deck.rudn.entity.dormitory.applications_list


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.dormitory.application_info.TypeDormApplication

@Serializable
data class DormitoryApplication(
    @SerialName("application_guid")
    val applicationGuid: String?,
    @SerialName("date_create")
    val dateCreate: String?,
    @SerialName("place")
    val place: String?,
    @SerialName("step_name")
    val stepName: String?,
    @SerialName("type_dorm_application")
    val typeDormApplication: TypeDormApplication?
)