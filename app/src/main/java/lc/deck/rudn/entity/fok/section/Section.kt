package lc.deck.rudn.entity.fok.section


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Section(
    @SerialName("description")
    val description: String?,
    @SerialName("icons")
    val icons: List<Icon>?,
    @SerialName("id")
    val id: String?,
    @SerialName("instructor")
    val instructor: Instructor?,
    @SerialName("name")
    val name: String?,
    @SerialName("section")
    val section: SectionX?,
    @SerialName("times")
    val times: List<Time>?,
    var isChecked: Boolean = false
) : java.io.Serializable