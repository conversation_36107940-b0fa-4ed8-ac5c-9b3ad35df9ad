package lc.deck.rudn.entity.enrollee.contract


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Document(
    @SerializedName("file")
    val `file`: String?,
    @SerializedName("format")
    val format: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("size")
    val size: String?,
    @SerializedName("needconfirm_file")
    val needConfirmFile: Boolean?
) : Parcelable