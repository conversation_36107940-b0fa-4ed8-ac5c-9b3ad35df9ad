package lc.deck.rudn.entity.profile.personal_data.personnel_data

data class LaborActivitiesOfRudnUniversity(
    val jobTitle: String?,
    val worksRemotely: Boolean?,
    val memberOfTheScientificCouncil: Boolean?,
    val memberOfTheRectorate: Boolean?,
    val tanNumber: String?,
    val workPhone: String?,
    val subdivision: String?,
    val headquarters: String?,
    val dateOfReceipt: String?,
    val dateOfDismissal: String?,
    val count: Double?,
    val receptionOrderNumber: String?,
    val receptionOrderDate: String?,
    val typeOccupancy: String?,
    val schedule: String?,
    val vacationInfo: String?
)
