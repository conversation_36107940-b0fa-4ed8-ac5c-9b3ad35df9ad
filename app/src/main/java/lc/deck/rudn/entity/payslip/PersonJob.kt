package lc.deck.rudn.entity.payslip

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PersonJob(
    @SerializedName("employee_guid")
    val employeeGuid: String?,
    @SerializedName("employee_id")
    val employeeId: Int?,
    @SerializedName("job_title")
    val jobTitle: String?,
    @SerializedName("table_number")
    val tableNumber: String?
) : Parcelable