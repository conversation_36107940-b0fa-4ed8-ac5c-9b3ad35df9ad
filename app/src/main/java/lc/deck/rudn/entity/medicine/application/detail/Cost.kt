package lc.deck.rudn.entity.medicine.application.detail


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.ui.payment.contracts.Converter

@Serializable
data class Cost(
    @SerialName("amount")
    val amount: Double,
    @SerialName("currency")
    val currency: String
){
    override fun toString(): String {
        return if (!currency.isNullOrEmpty())
            "${Converter.convertDebt(amount)} $currency"
        else  if (currency.isNullOrEmpty())
            Converter.convertDebt(amount)
        else ""
    }
}