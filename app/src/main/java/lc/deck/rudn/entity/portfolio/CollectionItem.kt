package lc.deck.rudn.entity.portfolio

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CollectionItem(
    @SerializedName("attachments")
    val attachments: List<Attachment>?,
    @SerializedName("base")
    val base: String?,
    @SerializedName("cause")
    val cause: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("type")
    val type: Type?
) : Parcelable
