package lc.deck.rudn.entity.enrollee.enrollee

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.employee.EduDivision

@Parcelize
data class TranslatorContractCreate(
    @SerializedName("edu_division")
    val eduDivision: String?,
    @SerializedName("application_guid")
    val applicationGuid: String?,
    @SerializedName("edu_division_name")
    val eduDivisionName: String?,
    @SerializedName("edu_form_guid")
    val eduFormGuid: String?,
) : Parcelable
