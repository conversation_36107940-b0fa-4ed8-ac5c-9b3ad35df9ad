package lc.deck.rudn.entity.phonebook

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PhonebookContact(
    @SerialName("id")
    val id: Int?,
    @SerialName("phone_number")
    val phoneNumber: String?,
    @SerialName("internal_number")
    val internalNumber: String?,
    @SerialName("fio")
    val fio: String?,
    @SerialName("email")
    val email: String?,
    @SerialName("position")
    val position: String?,
    @SerialName("structure")
    val structure: ContactItem?,
    @SerialName("substructure")
    val substructure: ContactItem?,
    @SerialName("subdivision")
    val subdivision: ContactItem?,
    @SerialName("category")
    val category: ContactItem?,
    @SerialName("cabinet")
    val cabinet: String?,
    @SerialName("address")
    val address: String?
)
