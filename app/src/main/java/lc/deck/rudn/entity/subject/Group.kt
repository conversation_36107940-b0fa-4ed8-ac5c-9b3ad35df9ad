package lc.deck.rudn.entity.subject

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.undergraduate_academ_group.UndergraduateAcademGroup

@Parcelize
data class Group(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String?,
    @SerializedName("faculty_id")
    val facultyId: Int?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("updated_at")
    val updatedAt: String?,
    @SerializedName("object_id")
    val objectId: Int?,
    @SerializedName("undergraduateAcademGroups")
    val undergraduateAcademGroups: List<UndergraduateAcademGroup>?,
    @SerializedName("curriculum_id")
    val curriculumId: Int?,
    @SerializedName("department_id")
    val departmentId: Int?,
    @SerializedName("workProgram")
    val subject: Subject?,
    @SerializedName("countOfEvents")
    val countOfEvents: Int?,
    @SerializedName("totalCount")
    val totalCount: Int?,
    @SerializedName("percentageOfVisits")
    val percentageOfVisits: Double?,
    @SerializedName("countOfUndergraduateAcademGroups")
    val countOfStudents: Int?,
    @SerializedName("current_semester")
    val currentSem: Semester?,
    @SerializedName("current_academ_year")
    val currentYear: AcademYear?
): Parcelable