package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class Statement(
    @SerializedName("academGroup")
    val academGroup: AcademGroup?,
    @SerializedName("academYear")
    val academYear: AcademYear?,
    @SerializedName("certification_date")
    val certificationDate: Date?,
    @SerializedName("finalControlForm")
    val finalControlForm: FinalControlForm?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("module")
    val module: Module?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("results_till_date")
    val resultsTillDate: String?,
    @SerializedName("semestr")
    val semestr: Semestr?,
    @SerializedName("statementStatus")
    val statementStatus: StatementStatus?,
    @SerializedName("statementType")
    val statementType: StatementType?,
    @SerializedName("students")
    val students: Students?,
    @SerializedName("studyGroup")
    val studyGroup: StudyGroup?,
    @SerializedName("subject")
    val subject: Subject?,
    @SerializedName("topic_needed")
    val topicNeeded: Boolean? = false,
    @SerializedName("undergraduateAcademGroups")
    val undergraduateAcademGroup: List<UndergraduateAcademGroup>?,
    @SerializedName("period_id")
    val periodId: Int?,
    @SerializedName("is_correction_request_allowed")
    val isCorrectionRequestAllowed: Boolean? = true,
    @SerializedName("faculty")
    val faculty: Faculty?,
    @SerializedName("is_reject_allowed")
    val isRejectAllowed: Boolean?,
    @SerializedName("practiceType")
    val practiceType: PracticeType?,
    @SerializedName("correction_id")
    val correctionId: Int?,
    @SerializedName("statementFile")
    val statementFile: StatementFile?
) : Parcelable