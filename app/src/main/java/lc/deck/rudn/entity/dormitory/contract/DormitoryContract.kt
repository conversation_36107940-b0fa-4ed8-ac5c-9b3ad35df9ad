package lc.deck.rudn.entity.dormitory.contract


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DormitoryContract(
    @SerialName("address")
    val address: String? = null,
    @SerialName("contract_guid")
    val contractGuid: String?,
    @SerialName("documents")
    val documents: List<Document?>?,
    @SerialName("end_date")
    val endDate: String?,
    @SerialName("enrollee")
    val enrollee: Enrollee?,
    @SerialName("payer")
    val payer: Payer?,
    @SerialName("paymentType")
    val paymentType: PaymentType?,
    @SerialName("room")
    val room: String? = null,
    @SerialName("start_date")
    val startDate: String?,
    @SerialName("sumPayment")
    val sumPayment: SumPayment?,
    @SerialName("contact_info")
    val contractInfo: ContractInfo?
)