package lc.deck.rudn.entity.applicant.agreement

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * Created by Seregaryz on 02.06.2022.
 */
@Parcelize
data class Agreement(

    @SerializedName("size")
    val size: String? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("base64")
    val base64: String? = null,

    @SerializedName("format")
    val format: String? = null,

    @SerializedName("guid")
    val guid: String? = null

): Parcelable
