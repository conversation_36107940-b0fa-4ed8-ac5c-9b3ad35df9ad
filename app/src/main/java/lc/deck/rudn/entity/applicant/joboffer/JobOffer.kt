package lc.deck.rudn.entity.applicant.joboffer


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class JobOffer(
    @SerializedName("department")
    val department: Department?,
    @SerializedName("duties")
    val duties: List<Duty>?,
    @SerializedName("form_of_employment")
    val formOfEmployment: FormOfEmployment?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("is_contract_allowed")
    val isContractAllowed: Boolean?,
    @SerializedName("position")
    val position: Position?,
    @SerializedName("rate")
    val rate: String?,
    @SerializedName("salary")
    val salary: Salary?,
    @SerializedName("working_regime")
    val workingRegime: WorkingRegime?
) : Parcelable