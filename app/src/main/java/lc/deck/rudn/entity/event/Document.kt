package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Document(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String?,
//    @SerializedName("comment")
//    val comment: String?,
//    @SerializedName("document_type_id")
//    val documentTypeId: Int?,
    @SerializedName("url")
    val url: String?,
//    @SerializedName("created_at")
//    val createdAt: Date?,
//    @SerializedName("updated_at")
//    val updatedAt: Date?,
    @SerializedName("documentType")
    val documentType: DocumentType?
): Parcelable