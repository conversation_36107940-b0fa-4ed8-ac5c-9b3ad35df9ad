package lc.deck.rudn.entity.payslip

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class SingleSelectedItem(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("value")
    val value: String?,
    @SerializedName("person_job")
    val personJob: PersonJob? = null,
    @SerializedName("type")
    val type: Int,
) : Parcelable {
    companion object {
        const val ITEM_TYPE_1 = 1
        const val ITEM_TYPE_2 = 2
        const val ITEM_TYPE_3 = 3
    }
}