package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Decision(
    @SerializedName("correctionStatusType")
    val correctionStatusType: CorrectionStatusType?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
):Parcelable{
    companion object{
        const val DECISION_1 = 1
        const val DECISION_2 = 2
        const val DECISION_3 = 3
    }
}