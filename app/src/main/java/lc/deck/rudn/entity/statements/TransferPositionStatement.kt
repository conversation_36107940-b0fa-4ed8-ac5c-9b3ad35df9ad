package lc.deck.rudn.entity.statements

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 0001 Перевод на другую должность
 */
@Parcelize
data class TransferPositionStatement(
    @SerializedName("applicationDate")
    override var applicationDate: String = "",
    @SerializedName("jobTitle")
    override var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    override var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    override var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    override var rate: Double = 0.0,
    @SerializedName("attachments")
    override var attachments: MutableList<Attachment> = mutableListOf(),
    @SerializedName("newJobTitle")
    val newJobTitle: JobTitle = JobTitle(),
    @SerializedName("newSubdivision")
    val newSubdivision: Subdivision = Subdivision(),
    @SerializedName("newBid")
    val newBid: Double = 0.0,
    @SerializedName("scheduleChange")
    val scheduleChange: Boolean = false,
    @SerializedName("schedule")
    val schedule: Schedule = Schedule(),
) : Statement() {

    fun toRequest() = TransferPositionStatementRequest(
        applicationDate,
        jobTitle,
        subdivision,
        headquarter,
        rate,
        attachments,
        newJobTitle,
        newSubdivision,
        newBid,
        scheduleChange,
        schedule
    )
}

@Parcelize
data class TransferPositionStatementRequest(
    @SerializedName("applicationDate")
    val applicationDate: String = "",
    @SerializedName("jobTitle")
    val jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    val subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    val headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    val rate: Double = 0.0,
    @SerializedName("attachments")
    val attachments: List<Attachment> = emptyList(),
    @SerializedName("newJobTitle")
    val newJobTitle: JobTitle = JobTitle(),
    @SerializedName("newSubdivision")
    val newSubdivision: Subdivision = Subdivision(),
    @SerializedName("newBid")
    val newBid: Double = 0.0,
    @SerializedName("scheduleChange")
    val scheduleChange: Boolean = false,
    @SerializedName("schedule")
    val schedule: Schedule = Schedule(),
): Parcelable
