package lc.deck.rudn.entity.journal.journal


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Journal(
    @SerialName("coefficient")
    val coefficient: Int?,
    @SerialName("department")
    val department: Department?,
    @SerialName("discipline")
    val discipline: Discipline?,
    @SerialName("events")
    val events: List<Event>? = null,
    @SerialName("eventsAmount")
    val eventsAmount: Int?,
    @SerialName("eventsDoneAmount")
    val eventsDoneAmount: Int?,
    @SerialName("id")
    val id: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("studyGroup")
    val studyGroup: StudyGroup?,
    @SerialName("studyPeriod")
    val studyPeriod: StudyPeriod?,
    @SerialName("teachers")
    val teachers: List<TeacherX>?,
    @SerialName("studentsNumber")
    val studentsNumber: Int?,
    @SerialName("attendancePercentage")
    val attendancePercentage: Int?,
    @SerialName("journalNeedFilling")
    val journalNeedFilling: Boolean?
)