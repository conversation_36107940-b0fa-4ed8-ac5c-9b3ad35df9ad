package lc.deck.rudn.entity.profile.personal_data

import lc.deck.rudn.entity.profile.personal_data.education.Education
import lc.deck.rudn.entity.profile.personal_data.general_information.GeneralInformation
import lc.deck.rudn.entity.profile.personal_data.personnel_data.PersonnelData

data class ProfilePersonalData(
    val generalInformation: GeneralInformation?,
    val education: Education?,
    val personnelData: PersonnelData?,
    val science: SciencePersonalData?
)
