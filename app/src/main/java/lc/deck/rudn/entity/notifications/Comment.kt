package lc.deck.rudn.entity.notifications


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.Person

@Parcelize
data class Comment(
    @SerializedName("role")
    val role: Int?, //1
    @SerializedName("event_id")
    val eventId: Int?, // 109
    @SerializedName("lesson_type")
    val lessonType: String?, // Практика
    @SerializedName("person_from")
    val personFrom: Person?,
    @SerializedName("subject_name")
    val subjectName: String?, // Менеджмент
    @SerializedName("parent_event_comment_id")
    val parentEventCommentId: Int?, // 251,
    @SerializedName("to_role_id")
    val toRoleId: Int?
):Parcelable