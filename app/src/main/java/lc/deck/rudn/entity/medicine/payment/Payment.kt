package lc.deck.rudn.entity.medicine.payment

import kotlinx.serialization.SerialName


@kotlinx.serialization.Serializable
data class Payment(
    @SerialName("account_number")
    val accountNumber: String,
    @SerialName("email")
    val email: String? = ""
)

@kotlinx.serialization.Serializable
data class ResponseCard(
    @SerialName("name")
    val name: String? = null,
    @SerialName("logo")
    val logo: String? = null,
    @SerialName("payment_url")
    val paymentUrl: String? = null
)