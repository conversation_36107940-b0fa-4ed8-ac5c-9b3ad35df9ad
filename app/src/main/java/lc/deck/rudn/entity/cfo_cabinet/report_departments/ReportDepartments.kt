package lc.deck.rudn.entity.cfo_cabinet.report_departments


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.cfo_cabinet.available_reports.Department

@Parcelize
data class ReportDepartments(
    @SerializedName("departments")
    val departments: List<Department?>?,
    @SerializedName("id")
    val id: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("date_range")
    val dateRange: Int?
) : Parcelable {

     companion object {
         const val DATE_RANGE_30 = 30
         const val DATE_RANGE_365 = 365
     }
}