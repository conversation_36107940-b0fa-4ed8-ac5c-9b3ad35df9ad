package lc.deck.rudn.entity.personnel_transactions

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 04.07.2022, понедельник
 **/
@Parcelize
data class SignatureAnswer(
    @SerializedName("success")
    val success: Boolean?,
    @SerializedName("error")
    val error: String?,
    @SerializedName("code")
    val code: Int,
    @SerializedName("task_guid")
    val taskGuid: String?,
    @SerializedName("Request_id")
    val requestId: String?,
    @SerializedName("phone")
    val phone: String?,
):Parcelable
