package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class EventWorkProgramSection(
    @SerializedName("id")
    val id: Int?,
//    @SerializedName("work_program_section_id")
//    val workProgramSectionId: Int?,
//    @SerializedName("control_form_id")
//    val controlFormId: Int?,
//    @SerializedName("employee_id")
//    val employeeId: Int?,
//    @SerializedName("created_at")
//    val createdAt: Date?,
//    @SerializedName("updated_at")
//    val updatedAt: Date?,
    @SerializedName("academ_groups")
    val academGroups: List<AcademGroup>?,
    @SerializedName("role")
    val role: Int?,
    @SerializedName("eventWorkProgramSectionEmployees")
    val eventWorkProgramSectionEmployees: List<TeachersWrapper>?,
    @SerializedName("workProgramSection")
    val workProgramSection: WorkProgramSection?,
    @SerializedName("event_number")
    val eventNumber: Int?
): Parcelable {
    companion object {
        const val ROLE_TEACHER = 1
        const val ROLE_STUDENT = 2
    }
}