package lc.deck.rudn.entity.student_check


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class StudentCheckBody(
    @SerializedName("academ_group_id")
    val academicGroupId: Int?,
    @SerializedName("event_id")
    val eventId: Int,
    @SerializedName("undergraduate_academ_groups")
    val studentCheckPersons: List<StudentCheckPerson>
): Parcelable