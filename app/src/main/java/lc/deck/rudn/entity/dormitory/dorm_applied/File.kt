package lc.deck.rudn.entity.dormitory.dorm_applied


import com.google.gson.annotations.SerializedName
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class File(
    @SerialName("base64")
    val base64: String?,
    @SerialName("format")
    val format: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("size")
    val size: Int?
)