package lc.deck.rudn.entity.dormitory.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PersonalData(
    @SerialName("surname")
    val surname: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("patronymic")
    val patronymic: String?,
    @SerialName("birth_date")
    val birthDate: String?,
    @SerialName("address")
    val address: String?,
    @SerialName("gender")
    val gender: Gender?,
    @SerialName("contactInfo")
    val contactInfo: ContactInfo?,
    @SerialName("citizenship")
    val citizenship: Citizenship?
)
