package lc.deck.rudn.entity.dormitory.apply_form


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DormitoryApplyForm(
    @SerialName("addfiles")
    val addfiles: List<Addfile?>? = null,
    @SerialName("comment_application")
    val commentApplication: String? = null,
    @SerialName("fact_address")
    val factAddress: String?,
    @SerialName("family_status_guid")
    val familyStatusGuid: String?,
    @SerialName("organization")
    val organization: Organization? = null,
    @SerialName("person_id")
    val personId: Int?,
    @SerialName("representatives")
    val representatives: List<Representative?>? = null,
    @SerialName("type_dorm_application")
    val typeDormApplication: String?
) 