package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcSearchResult(
    @SerializedName("id")
    val id: Int? = null,

	@SerializedName("show_description")
	val showDescription: Boolean? = null,

	@SerializedName("name")
	val name: String? = null,

    @SerializedName("alias")
    val alias: String?= null,
) : Parcelable
