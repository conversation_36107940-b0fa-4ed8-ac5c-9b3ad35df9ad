package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Room(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("territory_id")
    val territoryId: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("territory")
    val territory: Territory?
): Parcelable