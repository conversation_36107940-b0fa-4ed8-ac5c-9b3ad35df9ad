package lc.deck.rudn.entity.cfo_cabinet.available_reports


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Department(
    @SerializedName("group")
    val group: String?,
    @SerializedName("id")
    val id: String,
    @SerializedName("name")
    val name: String?,
    @SerializedName("is_selected")
    var isSelected: Boolean = false
) : Parcelable