package lc.deck.rudn.entity.iep.iep


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Optional(
    @SerialName("choiceDisciplines")
    val choiceDisciplines: ChoiceDisciplines?,
    @SerialName("mdk")
    val mdk: Mdk?,
    @SerialName("needRegistrationDisciplines")
    val needRegistrationDisciplines: Boolean?,
    @SerialName("needRegistrationMDK")
    val needRegistrationMDK: Boolean?
)