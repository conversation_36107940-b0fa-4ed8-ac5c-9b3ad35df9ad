package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Contract(
    @SerializedName("number")
    val number: String?,
    @SerializedName("conclusion_date")
    val conclusionDate: String?,
    @SerializedName("contract_name")
    val contractName: String?
) : Parcelable