package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ReferenceOrderStatusFlow(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String?,
    @SerializedName("datetime")
    val datetime: String?
) : Parcelable {
    companion object {
        const val ORDER_ID_DONE = 4
    }
}