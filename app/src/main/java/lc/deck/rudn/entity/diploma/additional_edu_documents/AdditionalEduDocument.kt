package lc.deck.rudn.entity.diploma.additional_edu_documents


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AdditionalEduDocument(
    @SerialName("diploma_id")
    val diplomaId: String?,
    @SerialName("date")
    val date: String?,
    @SerialName("course_name")
    val courseName: String?,
    @SerialName("course_description")
    val courseDescription: String?,
    @SerialName("number")
    val number: String?,
    @SerialName("type")
    val type: Type?,
    @SerialName("files")
    val files: List<File?>?
)