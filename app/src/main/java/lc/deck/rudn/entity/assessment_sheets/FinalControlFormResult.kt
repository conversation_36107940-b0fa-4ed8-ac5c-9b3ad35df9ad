package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FinalControlFormResult(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
) : Parcelable{
    companion object{
        const val EXAM_STATUS_ID = 1
        const val TEST_ID = 2
        const val PAPER_WORK_ID = 3
    }
}
