package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class SingleSelectionItem(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("type")
    val type: Int?,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("sums")
    val sums: Pair<String, List<CountSum?>?>? = null,
    @SerializedName("contract_uuid")
    val contractUuid: String? = null,
    @SerializedName("contract_period")
    val contractPeriod: ContractPeriod? = null,
    @SerializedName("admission_order_date")
    val admissionOrderDate: String? = null,
    @SerializedName("military_document_type_id")
    val militaryDocumentTypeId: Int? = null,
    @SerializedName("ext_id")
    val extId: String? = null,
    @SerializedName("documents")
    val documents: List<MfcDocumentSubItem>? = null,
    @SerializedName("contractType")
    val contractType: ContractType? = null,
    ) : Parcelable {
    companion object {
        const val ITEM_0 = 0
        const val ITEM_1 = 1
        const val ITEM_2 = 2
        const val ITEM_3 = 3
        const val ITEM_4 = 4
        const val ITEM_5 = 5
        const val ITEM_6 = 6
        const val ITEM_7 = 7
        const val ITEM_8 = 8
        const val ITEM_9 = 9
        const val ITEM_10 = 10
        const val ITEM_11 = 11
        const val ITEM_12 = 12
        const val ITEM_13 = 13
        const val ITEM_14 = 14
        const val ITEM_15 = 15
        const val ITEM_16 = 16
        const val ITEM_17 = 17
        const val ITEM_18 = 18
        const val ITEM_19 = 19
        const val ITEM_20 = 20
        const val ITEM_21 = 21
        const val ITEM_22 = 22
        const val ITEM_23 = 23
        const val ITEM_24 = 24
        const val ITEM_25 = 25
        const val ITEM_26 = 26
        const val ITEM_27 = 27
        const val ITEM_28 = 28
        const val ITEM_29 = 29
        const val ITEM_30 = 30
        const val ITEM_31 = 31
        const val ITEM_32 = 32
        const val ITEM_33 = 33
        const val ITEM_34 = 34
        const val ITEM_35 = 35
        const val ITEM_36 = 36
        const val ITEM_37 = 37
        const val ITEM_38 = 38
        const val ITEM_39 = 39
        const val ITEM_40 = 40
        const val ITEM_41 = 41
        const val ITEM_42 = 42
        const val ITEM_43 = 43
        const val ITEM_44 = 44
        const val ITEM_45 = 45
        const val ITEM_46 = 46
        const val ITEM_47 = 47
        const val ITEM_48 = 48
        const val ITEM_49 = 49
        const val ITEM_50 = 50
        const val ITEM_51 = 51
        const val ITEM_52 = 52
        const val ITEM_53 = 53
        const val ITEM_54 = 54

        const val ID_1 = 1
        const val ID_2 = 2
        const val ID_3 = 3
        const val ID_4 = 4
        const val ID_5 = 5
    }
}