package lc.deck.rudn.entity.applicant.department


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.applicant.joboffer.Department

@Parcelize
data class Visit(
    @SerializedName("departments")
    val departments: List<Department>?,
    @SerializedName("description")
    val description: List<String>?
) : Parcelable