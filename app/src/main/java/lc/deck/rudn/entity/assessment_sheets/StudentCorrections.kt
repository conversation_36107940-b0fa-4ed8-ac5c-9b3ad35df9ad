package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class StudentCorrections(
    @SerializedName("correction_id")
    val correctionId: Int? = null,
    @SerializedName("statement_student_id")
    val statementStudentId: Int? = null,
    @SerializedName("certification_result")
    val certificationResult: Int? = null,
    @SerializedName("is_absent")
    val isAbsent: Boolean,
    @SerializedName("is_mistake")
    val isMistake: Boolean? = false,
    @SerializedName("reason")
    val reason: String,
    @SerializedName("semestr_result")
    val semestrResult: Int? = null,
    @SerializedName("topic")
    val topic: String? = null,
    @SerializedName("topic_eng")
    val topicEng: String? = null
): Parcelable