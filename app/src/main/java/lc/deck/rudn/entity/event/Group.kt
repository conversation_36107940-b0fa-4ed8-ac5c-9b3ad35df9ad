package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.undergraduate_academ_group.UndergraduateAcademGroup

@Parcelize
data class Group(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String?,
    @SerializedName("faculty_id")
    val facultyId: Int?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("updated_at")
    val updatedAt: String?,
    @SerializedName("object_id")
    val objectId: Int?,
    @SerializedName("undergraduateAcademGroups")
    val undergraduateAcademGroups: List<UndergraduateAcademGroup>?,
    @SerializedName("noted")
    val noted: Boolean?
): Parcelable