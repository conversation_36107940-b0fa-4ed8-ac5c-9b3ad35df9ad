package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ActivityDirection(
    @SerializedName("id")
    val id: Int?, // 52
    @SerializedName("activity_direction_parent_id")
    val activityDirectionParentId: Int?, // 21
    @SerializedName("name")
    val name: String? // Культура
): Parcelable