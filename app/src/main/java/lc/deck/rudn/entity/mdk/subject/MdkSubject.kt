package lc.deck.rudn.entity.mdk.subject


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.mdk.filter.MdkTheme

@Parcelize
data class MdkSubject(
    @SerializedName("description")
    val description: String?,
    @SerializedName("faculty")
    val faculty: Faculty?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("is_request_available")
    val isRequestAvailable: Boolean?,
    @SerializedName("language_implementation_guid")
    val languageImplementationGuid: String?,
    @SerializedName("modules")
    val modules: Modules?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("thematic")
    val thematic: MdkTheme?,
    @SerializedName("status")
    val status: MdkSubjectStatus?
) : Parcelable