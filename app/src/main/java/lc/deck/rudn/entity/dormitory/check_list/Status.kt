package lc.deck.rudn.entity.dormitory.check_list


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Status(
    @SerialName("id")
    val id: Int?,
    @SerialName("name")
    val name: String? = null
) {

    companion object {
        const val ACTION_UNAVAILABLE = 1
        const val ACTION_AVAILABLE = 2
        const val ACTION_DONE = 3
        const val ACTION_PROCESSING = 4
        const val ACTION_FAILED = 5
    }
}