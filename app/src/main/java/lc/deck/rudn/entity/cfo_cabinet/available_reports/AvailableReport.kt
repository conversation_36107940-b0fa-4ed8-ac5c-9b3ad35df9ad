package lc.deck.rudn.entity.cfo_cabinet.available_reports


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AvailableReport(
    @SerializedName("departments")
    val departments: List<Department?>?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?
) : Parcelable