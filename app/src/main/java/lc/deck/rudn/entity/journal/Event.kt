package lc.deck.rudn.entity.journal


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Event(
    @SerialName("controlForm")
    var controlForm: String?,
    @SerialName("controlFormTheme")
    val controlFormTheme: String?,
    @SerialName("id")
    val id: String?,
    @SerialName("students")
    val students: MutableList<Student?>?,
    @SerialName("theme")
    val theme: String?
)