package lc.deck.rudn.entity.grades_book

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class GradesBookSemester(

	@SerializedName("semestr")
	val semestr: Semester? = null,

	@SerializedName("academ_year")
	val academYear: AcademYear? = null,

	@SerializedName("final_control_forms")
	val finalControlForms: List<FinalControlFormsItem?>? = null,

//	@SerializedName("module")
//	val module: Any? = null,

	@SerializedName("course")
	val course: Course? = null,

	@SerializedName("has_final_examination")
	val hasFinalExamination: Boolean? = null,

	@SerializedName("is_current")
	val isCurrent: Boolean? = null
) : Parcelable


