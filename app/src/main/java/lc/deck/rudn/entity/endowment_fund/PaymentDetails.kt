package lc.deck.rudn.entity.endowment_fund

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentDetails(
    @SerializedName("purpose")
    val purpose : String?,
    @SerializedName("bank_name")
    val bankName : String?,
    @SerializedName("inn")
    val inn : String?,
    @SerializedName("kpp")
    val kpp : String?,
    @SerializedName("ogrn")
    val ogrn : String?,
    @SerializedName("bik")
    val bik : String?,
    @SerializedName("correspondent_account")
    val correspondentAccount : String?,
    @SerializedName("checking_account")
    val checkingAccount : String?,
    @SerializedName("qr_code")
    val qrCode : String?,
    @SerializedName("currency")
    val currency : Currency?
) : Parcelable
