package lc.deck.rudn.entity.offer

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.File

@Parcelize
data class OfferLangItem(
    @SerializedName("images")
    val images: List<File?>? = null,
    @SerializedName("basePath")
    val basePath: String? = null,
    @SerializedName("lang_id")
    val langId: Int? = null,
    @SerializedName("lang")
    val lang: Lang? = null
): Parcelable