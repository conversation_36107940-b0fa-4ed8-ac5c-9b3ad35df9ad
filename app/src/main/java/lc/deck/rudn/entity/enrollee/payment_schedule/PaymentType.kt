package lc.deck.rudn.entity.enrollee.payment_schedule


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentType(
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("payment_sum")
    val paymentSum: String?,
) : Parcelable