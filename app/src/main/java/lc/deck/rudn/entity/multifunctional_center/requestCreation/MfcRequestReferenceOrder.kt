package lc.deck.rudn.entity.multifunctional_center.requestCreation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.multifunctional_center.LegalRepresentative

@Parcelize
data class MfcRequestReferenceOrder(
    @SerializedName("reference_id")
    val referenceId: Int?,
    @SerializedName("is_ecp")
    val isDigital: Boolean?,
    @SerializedName("is_paper")
    val isPaper: Boolean?,
    @SerializedName("count")
    val count: Int?,
    @SerializedName("reference_order_academ_year")
    val educationYear: ArrayList<String>?,
    @SerializedName("is_dismission")
    val dismission: Int?,
    @SerializedName("education_program_parent_id")
    val educationFormatId: Int?,
    @SerializedName("fio_latin")
    val fioLatin: String?,
    @SerializedName("fio_child")
    val childFio: String?,
    @SerializedName("child_birthday_type_id")
    val childType: Int?,
    @SerializedName("child_birthday")
    val childDate: String?,
    @SerializedName("admission_id")
    val admission: Int?,
    @SerializedName("doc_type_id")
    val docType: Int?,
    @SerializedName("order_source_id")
    val sourceId: Int?,
    @SerializedName("invoice_problem_type_id")
    val problemId: Int?,
    @SerializedName("graduate_intern_id")
    val graduateType: Int?,
    @SerializedName("place_of_demand")
    val placeOfDemand: String?,
    @SerializedName("privacyPolicy")
    val privacyPolicy: Boolean?,
    @SerializedName("duplicate_reason_id")
    val duplicateReasonId: Int?,
    @SerializedName("period_start")
    val periodStart: String?,
    @SerializedName("period_end")
    val periodEnd: String?,
    @SerializedName("calendar_year")
    val calendarYear: Int?,
    @SerializedName("return_reason_id")
    val returnReasonId: Int?,
    @SerializedName("mvd_id")
    val mvdId: Int?,
    @SerializedName("military_document_type_id")
    val militaryDocumentTypeId: Int?,
    @SerializedName("stock_categories_id")
    val stockCategoriesId: Int?,
    @SerializedName("military_personnel_id")
    val militaryPersonnelId: Int?,
    @SerializedName("military_rank_id")
    val militaryRankId: Int?,
    @SerializedName("category_of_fitness_id")
    val categoryOfFitnessId: Int?,
    @SerializedName("military_enlistment_office_id")
    val militaryEnlistmentOfficeId: Int?,
    @SerializedName("attitudes_military_service_id")
    val attitudesMilitaryServiceId: Int?,
    @SerializedName("number_vus")
    val numberVus: String?,
    @SerializedName("military_document_series")
    val militaryDocumentSeries: String?,
    @SerializedName("military_document_number")
    val militaryDocumentNumber: String?,
    @SerializedName("citizenship_id")
    val citizenshipId: Int? = null,
    @SerializedName("sex_id")
    val sexId: Int? = null,
    @SerializedName("education_level_id")
    val educationLevelId: Int? = null,
    @SerializedName("identity_card_id")
    val identityCard: Int? = null,
    @SerializedName("identity_card_issued_by")
    val identityCardIssuedBy: String? = null,
    @SerializedName("birthplace")
    val birthplace: String? = null,
    @SerializedName("registry")
    val registry: String? = null,
    @SerializedName("identity_card_series")
    val identityCardSeries: Int? = null,
    @SerializedName("identity_card_number")
    val identityCardNumber: String? = null,
    @SerializedName("identity_card_issue_date")
    val identityCardIssueDate: String? = null,
    @SerializedName("birthdate")
    val birthdate: String? = null,
    @SerializedName("registry_begin")
    val registryBegin: String? = null,
    @SerializedName("registry_end")
    val registryEnd: String? = null,
    @SerializedName("name_education")
    val nameEducation: String? = null,
    @SerializedName("name_education_specialty")
    val nameEducationSpecialty: String? = null,
    @SerializedName("separate_name")
    val separateName: String? = null,
    @SerializedName("separate_surname")
    val separateSurname: String? = null,
    @SerializedName("separate_patronymic")
    val separatePatronymic: String? = null,
    @SerializedName("separate_name_lat")
    val separateNameLat: String? = null,
    @SerializedName("separate_surname_lat")
    val separateSurnameLat: String? = null,
    @SerializedName("separate_patronymic_lat")
    val separatePatronymicLat: String? = null,
    @SerializedName("reason_shift_fio_id")
    val reasonShiftFioId: Int? = null,
    @SerializedName("date")
    val date: String? = null,
    @SerializedName("achievement_type_id")
    val achievementTypeId: Int? = null,
    @SerializedName("achievement_category_id")
    val achievementCategoryId: Int? = null,
    @SerializedName("achievement_level_id")
    val achievementLevelId: Int? = null,
    @SerializedName("academic_year_id")
    val academicYearId: Int? = null,
    @SerializedName("contract_id")
    val contractId: Int? = null,
    @SerializedName("currency_id")
    val currencyId: Int? = null,
    @SerializedName("on_contract_id")
    val onContractId: Int? = null,
    @SerializedName("reason_transfer_id")
    val reasonTransferId: Int? = null,
    @SerializedName("semester_id")
    val semesterId: Int? = null,
    @SerializedName("transfer_option_id")
    val transferOptionId: Int? = null,
    @SerializedName("transfer_option_id")
    val legalRepresentative: LegalRepresentative? = null,
    @SerializedName("driving_certificate_category_id")
    val drivingCertificateCategoryId: Int? = null,
    @SerializedName("driving_certificate_category_presence")
    val drivingCertificateCategoryPresence: Int? = null,
    @SerializedName("driving_program_id")
    val drivingProgramId: Int? = null,
    @SerializedName("education_type_id")
    val educationTypeId: Int? = null,
    @SerializedName("work_place")
    val workPlace: String? = null,
    @SerializedName("personal_data_confirm")
    val personalDataConfirm: Int? = null,
    @SerializedName("education_level_extended_id")
    val educationLevelExtendedId: Int? = null,
    @SerializedName("faculty_id")
    val facultyId: Int? = null,
    @SerializedName("finance_type_id")
    val financeTypeId: Int? = null,
    @SerializedName("payment_period_id")
    val paymentPeriodId: Int? = null,
    @SerializedName("speciality_id")
    val specialityId: Int? = null,
    @SerializedName("birth_city")
    val birthCity: String? = null,
    @SerializedName("reason_budget_id")
    val reasonBudgetId: Int? = null,
    @SerializedName("client_temporary_address")
    val clientTemporaryAddress: String? = null,
    @SerializedName("mother_temporary_address")
    val motherTemporaryAddress: String? = null,
    @SerializedName("father_temporary_address")
    val fatherTemporaryAddress: String? = null,
    @SerializedName("archive_visit_reason_id")
    val archiveVisitReasonId: Int? = null,
    @SerializedName("visit_time")
    val visitTime: String? = null,
    @SerializedName("week_pregnancy")
    val weekPregnancy: String? = null,
    @SerializedName("date_reference")
    val dateReference: String? = null,
    @SerializedName("birthCertificates")
    val birthCertificates: BirthCertificates? = null,
    @SerializedName("allow_visit")
    val allowVisit: Int? = null,
    @SerializedName("faculty_archive")
    val facultyArchive: String? = null,
    @SerializedName("speciality_archive")
    val specialityArchive: String? = null,
    @SerializedName("year_archive")
    val yearArchive: String? = null,
    @SerializedName("nld_archive")
    val nldArchive: String? = null,
) : Parcelable