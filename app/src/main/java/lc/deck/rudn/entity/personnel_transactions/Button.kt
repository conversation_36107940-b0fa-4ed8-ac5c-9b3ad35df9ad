package lc.deck.rudn.entity.personnel_transactions


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Button(
    @SerializedName("is_comment_must")
    val isCommentMust: Boolean?,
    @SerializedName("is_main")
    val isMain: Boolean?,
    @SerializedName("label")
    val label: String?
) : Parcelable{
    companion object{
        const val MAIN = true
        const val NOT_MAIN = false
    }
}