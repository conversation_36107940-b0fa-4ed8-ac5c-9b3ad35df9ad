package lc.deck.rudn.entity.iep.iep


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChoiceDisciplines(
    @SerialName("blocks")
    val blocks: List<Block>?,
    @SerialName("choiceDisciplinesNeededCount")
    val choiceDisciplinesNeededCount: Int?,
    @SerialName("choiceDisciplinesSelectedCount")
    val choiceDisciplinesSelectedCount: Int?
)