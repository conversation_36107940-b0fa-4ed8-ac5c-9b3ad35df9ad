package lc.deck.rudn.entity.diploma_confirmation

import kotlinx.android.parcel.Parcelize
import android.os.Parcelable
import com.google.gson.annotations.SerializedName

@Parcelize
data class Subject(
    @SerializedName("ECTS")
    val ects: String? = null,
    @SerializedName("ZET")
    val zet: String? = null,
    @SerializedName("canCorrect")
    val canCorrect: Boolean? = null,
    @SerializedName("canShow")
    val canShow: Boolean? = null,
    @SerializedName("comment")
    var comment: String? = null,
    @SerializedName("credits")
    val credits: String? = null,
    @SerializedName("hours")
    val hours: Int? = null,
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("isCorrected")
    var isCorrected: Boolean? = null,
    @SerializedName("mark")
    val mark: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("nationalGrade")
    val nationalGrade: String? = null,
    @SerializedName("showInDiploma")
    var showInDiploma: Boolean? = null,
    @SerializedName("typeEnum")
    val typeEnum: Int? = null,
    @SerializedName("value")
    val value: String? = null
) : Parcelable
