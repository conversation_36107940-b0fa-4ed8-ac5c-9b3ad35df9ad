package lc.deck.rudn.entity.diploma_confirmation

import kotlinx.android.parcel.Parcelize
import android.os.Parcelable
import com.google.gson.annotations.SerializedName

@Parcelize
data class SemestersInfo(
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("subjects")
    val subjects: List<Subject>?,
    @SerializedName("isChecked")
    var isChecked: Boolean = false,
) : Parcelable
