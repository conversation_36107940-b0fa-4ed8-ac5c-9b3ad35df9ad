package lc.deck.rudn.entity.clinical_diagnostic_center.certificates


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Certificate(
    @SerialName("creationDate")
    val creationDate: String?,
    @SerialName("endDate")
    val endDate: String?,
    @SerialName("fileID")
    val fileID: String?,
    @SerialName("id")
    val id: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("number")
    val number: String?,
    @SerialName("startDate")
    val startDate: String?
)