package lc.deck.rudn.entity.dormitory.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LegalRepresentative(
    @SerialName("guid")
    val guid: String?,
    @SerialName("person_uuid")
    val personUuid: String?,
    @SerialName("rudn_id")
    val rudnId: String?,
    @SerialName("representativeType")
    val representativeType: RepresentativeType?,
    @SerialName("surname")
    val surname: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("patronymic")
    val patronymic: String?,
    @SerialName("birth_date")
    val birthDate: String?,
    @SerialName("email")
    val email: String?,
    @SerialName("phone_number")
    val phoneNumber: String?,
    @SerialName("documents")
    val documents: Documents?,
)
