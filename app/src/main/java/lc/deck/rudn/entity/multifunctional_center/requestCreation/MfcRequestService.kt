package lc.deck.rudn.entity.multifunctional_center.requestCreation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcRequestService(
    @SerializedName("service_type_id")
    val serviceTypeId: Int?,
    @SerializedName("date")
    val date: String?,
    @SerializedName("plan_time")
    val planTime: String?,
    @SerializedName("order_source_id")
    val sourceId: Int
) : Parcelable