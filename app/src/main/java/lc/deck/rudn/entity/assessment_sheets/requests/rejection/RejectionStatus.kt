package lc.deck.rudn.entity.assessment_sheets.requests.rejection

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RejectionStatus(

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("id")
    val id: Int? = null

) : Parcelable {
    companion object {
        const val REJECTION_STATUS_TYPE_1 = 1
        const val REJECTION_STATUS_TYPE_2 = 2
        const val REJECTION_STATUS_TYPE_3 = 3
    }
}