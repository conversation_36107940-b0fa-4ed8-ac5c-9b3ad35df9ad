package lc.deck.rudn.entity.profile.personal_data.scientific_activity

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Projects(
    @SerialName("name")
    val name: String?,
    @SerialName("from")
    val from: String?,
    @SerialName("to")
    val to: String?,
    @SerialName("year")
    val year: String?,
    @SerialName("contractSum")
    val contractSum: Double?,
    @SerialName("fulfilledIncome")
    val fulfilledIncome: Double?,
    @SerialName("registrationNumber")
    val registrationNumber: String?,
)
