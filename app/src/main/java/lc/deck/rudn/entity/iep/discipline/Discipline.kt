package lc.deck.rudn.entity.iep.discipline


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Discipline(
    @SerialName("blocks")
    val blocks: List<Block>?,
    @SerialName("choiceDisciplinesNeededCount")
    val choiceDisciplinesNeededCount: Int?,
    @SerialName("choiceDisciplinesSelectedCount")
    val choiceDisciplinesSelectedCount: Int?,
    @SerialName("disciplineSelectionDeadlineDate")
    val disciplineSelectionDeadlineDate: String?
)