package lc.deck.rudn.entity.medicine.medicine_service


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.ui.payment.contracts.Converter

@Serializable
data class Cost(
    @SerialName("amount")
    val amount: Double? = null,
    @SerialName("currency")
    val currency: String? = null
) {
    override fun toString(): String {
        return if (amount != null && !currency.isNullOrEmpty())
            "${Converter.convertDebt(amount)} $currency"
        else  if (amount != null && currency.isNullOrEmpty())
            Converter.convertDebt(amount)
        else ""
    }
}