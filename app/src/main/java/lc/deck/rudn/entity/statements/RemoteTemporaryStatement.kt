package lc.deck.rudn.entity.statements

import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 0004 Временный перевод на дистанционку
 */
@Parcelize
data class RemoteTemporaryStatement(
    @SerializedName("applicationDate")
    override var applicationDate: String = "",
    @SerializedName("jobTitle")
    override var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    override var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    override var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    override var rate: Double = 0.0,
    @SerializedName("attachments")
    override var attachments: MutableList<Attachment> = mutableListOf(),
    @SerializedName("reasoning")
    var reasoning: String = "",
    @SerializedName("continuously")
    val continuously: Boolean? = null,
    @SerializedName("applicationDateEnd")
    var applicationDateEnd: String = "",
    @SerializedName("schedule")
    val schedule: Schedule = Schedule(),
) : Statement() {

    fun toRequest() = RemoteTemporaryStatementRequest(
        applicationDate,
        jobTitle,
        subdivision,
        headquarter,
        rate,
        attachments,
        reasoning,
        continuously,
        applicationDateEnd,
        schedule
    )
}

data class RemoteTemporaryStatementRequest(
    @SerializedName("applicationDate")
    val applicationDate: String = "",
    @SerializedName("jobTitle")
    val jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    val subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    val headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    val rate: Double = 0.0,
    @SerializedName("attachments")
    val attachments: List<Attachment> = emptyList(),
    @SerializedName("reasoning")
    val reasoning: String = "",
    @SerializedName("continuously")
    val continuously: Boolean? = null,
    @SerializedName("applicationDateEnd")
    var applicationDateEnd: String = "",
    @SerializedName("schedule")
    val schedule: Schedule = Schedule(),
)
