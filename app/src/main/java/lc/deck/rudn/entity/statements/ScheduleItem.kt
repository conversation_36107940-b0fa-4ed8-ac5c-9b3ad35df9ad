package lc.deck.rudn.entity.statements

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ScheduleItem(
    @SerializedName("remote")
    var remote: Boolean? = null,
    @SerializedName("timeStart")
    var timeStart: String = "00:00",
    @SerializedName("timeEnd")
    var timeEnd: String = "00:00",
): Parcelable {

    fun getTotalHoursMinutes(): Pair<Int, Int> {
        if (remote == null) return Pair(0, 0)
        var startHour = timeStart.substringBefore(":").toInt()
        val startMinute = timeStart.substringAfter(":").toInt()
        var endHour = timeEnd.substringBefore(":").toInt()
        val endMinute = timeEnd.substringAfter(":").toInt()
        if (endHour == 0) endHour = 24
        if (startHour == 0) startHour = 0
        val dinnerBias = if ((endHour - startHour) >= 5) 1 else 0
        var resultHour = endHour - startHour - dinnerBias
        var resultMinute = endMinute - startMinute
        if (resultMinute < 0) {
            --resultHour
            resultMinute = 30
        }
        return Pair(resultHour, resultMinute)
    }
}
