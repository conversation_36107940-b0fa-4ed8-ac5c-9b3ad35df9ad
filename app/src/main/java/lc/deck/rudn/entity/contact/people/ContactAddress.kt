package lc.deck.rudn.entity.contact.people

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContactAddress(

    @SerializedName("house_num")
	val houseNum: String? = null,

    @SerializedName("city")
	val city: AddressCity? = null,

    @SerializedName("street")
	val street: AddressStreet? = null,

    @SerializedName("fullName")
	val fullName: String? = null,

    @SerializedName("id")
	val id: Int? = null,

    @SerializedName("street_id")
	val streetId: Int? = null,

    @SerializedName("building")
	val building: String? = null,

    @SerializedName("room")
	val room: String? = null,

    @SerializedName("city_id")
	val cityId: Int? = null
): Parcelable
