package lc.deck.rudn.entity.profile.personal_data.scientific_activity

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Ria(
    @SerialName("work")
    val work: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("type")
    val type: String?,
    @SerialName("authors")
    val authors: String?,
    @SerialName("applicationNumber")
    val applicationNumber: String?,
    @SerialName("applicationDate")
    val applicationDate: String?,
    @SerialName("registrationNumber")
    val registrationNumber: String?,
    @SerialName("registrationDate")
    val registrationDate: String?
)
