package lc.deck.rudn.entity.applicant.attach_files


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.File

@Parcelize
data class Document(
    @SerializedName("description")
    val description: String?,
    @SerializedName("docs_count")
    val docsCount: Int? = 1,
    @SerializedName("format")
    val format: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("is_must")
    val isMust: Boolean?,
    @SerializedName("size")
    val size: String?,
    @SerializedName("title")
    val title: String?,

    @SerializedName("loaded_files")
    val loadedFiles: MutableList<LoadedFiles?>? = mutableListOf(),
    @SerializedName("is_loaded")
    var loadedCount: Int? = 0,

    ) : Parcelable

@Parcelize
data class LoadedFiles(
    @SerializedName("file_name")
    val fileId: Int?,
    @SerializedName("file_name")
    val fileTitle: String? = "",
    @SerializedName("file_description")
    val fileDescription: String? = "",
    @SerializedName("file_path")
    val filePath: String? = "",
    @SerializedName("file")
    val file: File? = null,
) : Parcelable