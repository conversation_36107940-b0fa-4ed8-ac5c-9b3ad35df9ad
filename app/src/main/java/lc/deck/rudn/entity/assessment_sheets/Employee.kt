package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.employee.EmployeePosition

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>z
 * @created : 19,сентябрь,2022
 */

@Parcelize
data class Employee(
    @SerializedName("id")
    val id: Int? = null,

    @SerializedName("employeePosition")
    val employeePosition: EmployeePosition?
): Parcelable
