package lc.deck.rudn.entity.personnel_transactions


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Status(
    @SerializedName("guid")
    val guid: Int?,
    @SerializedName("name")
    val name: String?
) : Parcelable {
    companion object {
        const val STATUS_1 = 1
        const val STATUS_2 = 2
    }
}