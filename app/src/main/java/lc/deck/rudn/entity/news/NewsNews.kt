package lc.deck.rudn.entity.news


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class NewsNews(
//    @SerializedName("id")
//    val id: Int?,
//    @SerializedName("news_category_id")
//    val newsCategoryId: Int?,
//    @SerializedName("is_deleted")
//    val isDeleted: Boolean?,
    @SerializedName("newsCategoryLang")
    val newsCategoryLang: NewsCategoryLang?
): Parcelable