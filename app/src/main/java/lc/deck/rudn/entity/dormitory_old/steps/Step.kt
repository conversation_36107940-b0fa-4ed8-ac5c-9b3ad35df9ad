package lc.deck.rudn.entity.dormitory_old.steps

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Step(
    @SerializedName("button")
    val button: String? = "",
    @SerializedName("description")
    val description: String?,
    @SerializedName("guid")
    val guid: Int?,
    @SerializedName("order")
    val order: Int?,
    @SerializedName("step_status")
    val stepStatus: StepStatus?,
    @SerializedName("title")
    val title: String?
) : Parcelable