package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RefuseReason(

	@SerializedName("name")
	val name: String? = null,

	@SerializedName("id")
	val id: Int? = null,
    @SerializedName("is_selected")
    var isSelected: Boolean = false,
    @SerializedName("edited_text")
    var editedText: String? = null
) : Parcelable
