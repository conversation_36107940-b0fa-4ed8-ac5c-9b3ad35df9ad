package lc.deck.rudn.entity.employee

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.enrollee.application.EduForm

@Parcelize
class EduDivision(
    @SerializedName("edu_division_guid")
    val eduDivisionGuid: String?,
    @SerializedName("edu_division_name")
    val eduDivisionName: String?,
    @SerializedName("edu_form")
    val eduFormList: List<EduForm>
): Parcelable