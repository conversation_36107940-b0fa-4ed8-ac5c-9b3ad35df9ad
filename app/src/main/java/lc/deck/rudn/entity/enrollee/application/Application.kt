package lc.deck.rudn.entity.enrollee.application


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Application(
    @SerializedName("applicationStatus")
    val applicationStatus: ApplicationStatus?,
    @SerializedName("cost")
    val cost: Cost?,
    @SerializedName("eduForm")
    val eduForm: EduForm?,
    @SerializedName("eduLevel")
    val eduLevel: EduLevel?,
    @SerializedName("eduProfile")
    val eduProfile: EduProfile?,
    @SerializedName("edu_profile_code")
    val eduProfileCode: String?,
    @SerializedName("eduProgram")
    val eduProgram: EduProgram?,
    @SerializedName("faculty")
    val faculty: Faculty?,
    @SerializedName("fundingType")
    val fundingType: FundingType?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("is_contract_allowed")
    val isContractAllowed: Boolean?,
    @SerializedName("language")
    val language: String?,
    @SerializedName("place")
    val place: String?,
    @SerializedName("total_places")
    val totalPlaces: String?,
    @SerializedName("translator_available")
    val translatorAvailable: Boolean?,
    @SerializedName("person_confirm_sms_datatime")
    val personConfirmSmsDatatime: String?,
    @SerializedName("need_representative_confirm")
    val needRepresentativeConfirm: Boolean?
) : Parcelable