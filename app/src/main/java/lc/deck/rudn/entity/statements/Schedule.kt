package lc.deck.rudn.entity.statements

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Schedule(
    @SerializedName("monday")
    val monday: ScheduleItem = ScheduleItem(),
    @SerializedName("tuesday")
    val tuesday: ScheduleItem = ScheduleItem(),
    @SerializedName("wednesday")
    val wednesday: ScheduleItem = ScheduleItem(),
    @SerializedName("thursday")
    val thursday: ScheduleItem = ScheduleItem(),
    @SerializedName("friday")
    val friday: ScheduleItem = ScheduleItem(),
    @SerializedName("saturday")
    val saturday: ScheduleItem = ScheduleItem(),
    @SerializedName("sunday")
    val sunday: ScheduleItem = ScheduleItem(),
) : Parcelable {

    fun getTotalHoursAndMinutes(): Pair<Int, Int> {
        var totalHours = monday.getTotalHoursMinutes().first + tuesday.getTotalHoursMinutes().first +
                wednesday.getTotalHoursMinutes().first + thursday.getTotalHoursMinutes().first +
                friday.getTotalHoursMinutes().first + saturday.getTotalHoursMinutes().first +
                sunday.getTotalHoursMinutes().first

        val totalMinutes = monday.getTotalHoursMinutes().second + tuesday.getTotalHoursMinutes().second +
                wednesday.getTotalHoursMinutes().second + thursday.getTotalHoursMinutes().second +
                friday.getTotalHoursMinutes().second + saturday.getTotalHoursMinutes().second +
                sunday.getTotalHoursMinutes().second

        val minutesToHours = totalMinutes / 60

        val minutes = totalMinutes % 60

        totalHours += minutesToHours

        return Pair(totalHours, minutes)
    }

    fun getTotalHoursDouble(): Double {
        var totalHours: Double = (monday.getTotalHoursMinutes().first + tuesday.getTotalHoursMinutes().first +
                wednesday.getTotalHoursMinutes().first + thursday.getTotalHoursMinutes().first +
                friday.getTotalHoursMinutes().first + saturday.getTotalHoursMinutes().first +
                sunday.getTotalHoursMinutes().first).toDouble()

        val totalMinutes = monday.getTotalHoursMinutes().second + tuesday.getTotalHoursMinutes().second +
                wednesday.getTotalHoursMinutes().second + thursday.getTotalHoursMinutes().second +
                friday.getTotalHoursMinutes().second + saturday.getTotalHoursMinutes().second +
                sunday.getTotalHoursMinutes().second

        val minutesToHours = totalMinutes / 60

        val minutes = totalMinutes % 60

        totalHours += minutesToHours

        val result: Double = if (minutes > 0) totalHours + 0.5 else totalHours

        return result
    }
}
