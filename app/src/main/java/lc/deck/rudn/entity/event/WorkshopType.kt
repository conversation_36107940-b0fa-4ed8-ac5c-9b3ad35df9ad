package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class WorkshopType(
//    @SerializedName("id")
//    val id: Int?,
//    @SerializedName("parent_workshop_type_id")
//    val parentWorkshopTypeId: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("name_plural")
    val namePlural: String?
): Parcelable