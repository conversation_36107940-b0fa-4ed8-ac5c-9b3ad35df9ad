package lc.deck.rudn.entity.dormitory.dorm_applied


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AppyFile(
    @SerialName("format")
    val format: String?,
    @SerialName("guid")
    val guid: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("size")
    val size: String?,
    @SerialName("task_guid")
    val taskGuid: String?
) 