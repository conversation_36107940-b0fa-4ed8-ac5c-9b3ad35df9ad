package lc.deck.rudn.entity.dormitory.check_in.set_time

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CheckInTimeSet(
    @SerialName("application_guid")
    val applicationGuid: String?,
    @SerialName("person_id")
    val personId: Int?,
    @SerialName("arrival_date")
    val arrivalDate: String,
    @SerialName("arrival_time")
    val arrivalTime: String,
)