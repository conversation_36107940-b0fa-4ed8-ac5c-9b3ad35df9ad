package lc.deck.rudn.entity.dormitory.contract


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Document(
    @SerialName("confirm_SMS_phone")
    val confirmSMSPhone: String?,
    @SerialName("file")
    val `file`: String? = null,
    @SerialName("format")
    val format: String?,
    @SerialName("guid")
    val guid: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("needconfirm_file")
    val needconfirmFile: Boolean?,
    @SerialName("size")
    val size: String?
)