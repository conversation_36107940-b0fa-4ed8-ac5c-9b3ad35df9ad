package lc.deck.rudn.entity.multifunctional_center.preinfo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcTemplateBlocks(
    @SerializedName("block_type_id")
    val blockTypeId: Int,
    @SerializedName("order")
    val order: Int?,
    @SerializedName("serviceTemplateBlockText")
    val blockText: MfcServiceTemplateBlockText,
    @SerializedName("serviceTemplateBlockList")
    val serviceTemplateBlockList: MfcServiceTemplateBlock
) : Parcelable