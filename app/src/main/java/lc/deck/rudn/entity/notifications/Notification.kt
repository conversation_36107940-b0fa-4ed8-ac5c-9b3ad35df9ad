package lc.deck.rudn.entity.notifications


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.Person
import java.util.*

@Parcelize
data class Notification(
    @SerializedName("id")
    val id: Int? = null, // 428
    @SerializedName("notification_category_id")
    val notificationCategoryId: Int?, // 5
    @SerializedName("person_id")
    val personId: Int? = null, // 1
    @SerializedName("fromPerson")
    val fromPerson: Person? = null,
    @SerializedName("is_viewed")
    var isViewed: Boolean? = false, // true
    @SerializedName("created_at")
    val createdAt: Date?, // 2020-08-25 07:20:04
    @SerializedName("updated_at")
    val updatedAt: String? = null, // 2020-08-25 08:21:53
    @SerializedName("from_person_id")
    val fromPersonId: Int? = null, // 2
    @SerializedName("action_id")
    val actionId: Int? = null, // 5
    @SerializedName("payload")
    val payload: Payload?,
    @SerializedName("notificationCategory")
    val notificationCategory: NotificationCategory?,
    @SerializedName("text")
    val text: String? // Преподаватель ответил на комментарий студента
): Parcelable {
    companion object {
        const val CATEGORY_COMMENT = 5
    }
}