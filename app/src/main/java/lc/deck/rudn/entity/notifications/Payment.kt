package lc.deck.rudn.entity.notifications

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Payment(
    @SerializedName("text")
    val text:String?,
    @SerializedName("number")
    val number:String?,
    @SerializedName("button")
    val button:String,
    @SerializedName("contract_guid")
    val contractGuid:String?
) : Parcelable