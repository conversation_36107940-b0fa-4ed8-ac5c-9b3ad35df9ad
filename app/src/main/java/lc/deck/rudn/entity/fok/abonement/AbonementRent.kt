package lc.deck.rudn.entity.fok.abonement

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.fok.section.SectionRegistration

@Serializable
data class AbonementRent(
    @SerialName("personID")
    val personId: String?,
    @SerialName("abonementID")
    val abonementId: String?,
    @SerialName("duration")
    val duration: Int?,
    @SerialName("sections")
    val sections: List<SectionRegistration>?
    )