package lc.deck.rudn.entity.medicine.attachement


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Document(
    @SerialName("formats")
    val formats: String,
    @SerialName("guid")
    val guid: String,
    @SerialName("is_must")
    val isMust: <PERSON><PERSON><PERSON>,
    @SerialName("name")
    val name: String,
    @SerialName("number_of_files")
    val numberOfFiles: Int,

    @SerialName("loaded_files")
    val loadedFiles: MutableList<LoadedFiles?>? = mutableListOf(),
    @SerialName("is_loaded")
    var loadedCount: Int? = 0,
)

@Serializable
data class LoadedFiles(
    @SerialName("file_id")
    val fileId: Int?,
    @SerialName("file_name")
    val fileTitle: String? = "",
    @SerialName("file_description")
    val fileDescription: String? = "",
    @SerialName("file_format")
    val fileFormat: String? = "",
    @SerialName("base64")
    val base64: String? = "",
)