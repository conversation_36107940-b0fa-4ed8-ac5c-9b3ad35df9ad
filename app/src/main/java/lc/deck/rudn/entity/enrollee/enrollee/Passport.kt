package lc.deck.rudn.entity.enrollee.enrollee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Passport(
    @SerializedName("documentType")
    var documentType: DocumentType?,
    @SerializedName("issue_date")
    var issueDate: String?,
    @SerializedName("issued_by")
    var issuedBy: String?,
    @SerializedName("issuer_code")
    var issuerCode: String?,
    @SerializedName("series_number")
    var seriesNumber: String?,
    @SerializedName("series")
    var series: String?,
    @SerializedName("number")
    var number: String?
) : Parcelable
{
    override fun toString(): String {
        return super.toString()
    }
}