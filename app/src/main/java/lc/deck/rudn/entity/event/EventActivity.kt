package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class EventActivity(
    @SerializedName("id")
    val id: Int?,
//    @SerializedName("activity_id")
//    val activityId: Int?,
//    @SerializedName("date_start")
//    val dateStart: Date?,
//    @SerializedName("date_end")
//    val dateEnd: Date?,
//    @SerializedName("street")
//    val street: String?,
//    @SerializedName("house_number")
//    val houseNumber: String?,
//    @SerializedName("flat_number")
//    val flatNumber: String?,
//    @SerializedName("comment")
//    val comment: String?,
//    @SerializedName("updated_at")
//    val updatedAt: Date?,
//    @SerializedName("created_at")
//    val createdAt: Date?,
    @SerializedName("activity")
    val activity: Activity?
): Parcelable