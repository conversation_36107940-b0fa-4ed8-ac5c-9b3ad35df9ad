package lc.deck.rudn.entity.dormitory.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Passport(
    @SerialName("documentType")
    val documentType: DocumentType?,
    @SerialName("series_number")
    val seriesNumber: String?,
    @SerialName("issue_date")
    val issueDate: String?,
    @SerialName("issued_by")
    val issuedBy: String?,
    @SerialName("issuer_code")
    val issuerCode: String?,
)
