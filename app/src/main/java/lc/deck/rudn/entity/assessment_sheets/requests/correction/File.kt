package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.*

@Parcelize
data class File(
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("hash")
    val hash: String?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("is_public")
    val isPublic: Boolean?,
    @SerializedName("path")
    val path: String?,
    @SerializedName("url")
    val url: String?,
    @SerializedName("user_id")
    val userId: Int?
):Parcelable