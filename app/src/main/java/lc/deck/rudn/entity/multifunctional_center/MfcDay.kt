package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcDay(
    @SerializedName("fullDate")
    val fullDate: String,
    @SerializedName("dayNumber")
    val dayNumber: String,
    @SerializedName("times")
    val times: List<MfcTime?>?,
    @SerializedName("isSelected")
    var isSelected: Boolean? = false,
):Parcelable