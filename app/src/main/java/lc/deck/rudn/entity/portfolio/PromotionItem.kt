package lc.deck.rudn.entity.portfolio

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromotionItem(
    @SerializedName("achievementDate")
    val achievementDate: String?,
    @SerializedName("achievementLevel")
    val achievementLevel: AchievementLevel?,
    @SerializedName("achievementPlace")
    val achievementPlace: String?,
    @SerializedName("achievementYear")
    val achievementYear: String?,
    @SerializedName("attachments")
    val attachments: List<Attachment>?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("status")
    val status: Status?,
    @SerializedName("type")
    val type: Type?
) : Parcelable
