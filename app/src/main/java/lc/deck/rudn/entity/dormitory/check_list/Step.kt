package lc.deck.rudn.entity.dormitory.check_list


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Step(
    @SerialName("description")
    val description: String? = null,
    @SerialName("order")
    val order: Int?,
    @SerialName("status")
    val status: Status? = null,
    @SerialName("title")
    val title: String?
) {
    companion object {
        const val ORDER_ID_1 = 1
        const val ORDER_ID_2 = 2
        const val ORDER_ID_3 = 3
        const val ORDER_ID_4 = 4
        const val ORDER_ID_5 = 5
        const val ORDER_ID_6 = 6
    }
}