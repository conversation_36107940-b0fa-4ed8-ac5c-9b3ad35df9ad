package lc.deck.rudn.entity.clinical_diagnostic_center.vaccines

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AddVaccine(
    @SerialName("personID")
    val personId: Int?,
    @SerialName("vaccineID")
    val vaccineId: String?,
    @SerialName("vaccineName")
    val vaccineName: String?,
    @SerialName("vaccineDate")
    val vaccineDate: String?,
    @SerialName("comment")
    val comment: String?
)
