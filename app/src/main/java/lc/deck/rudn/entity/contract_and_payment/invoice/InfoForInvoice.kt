package lc.deck.rudn.entity.contract_and_payment.invoice


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class InfoForInvoice(
    @SerializedName("car_brand")
    val carBrand: String,
    @SerializedName("car_model")
    val carModel: String,
    @SerializedName("email_id")
    val emailId: String,
    @SerializedName("offer_number")
    val offerNumber: String,
    @SerializedName("parking_start")
    val parkingStart: String,
    @SerializedName("person_id")
    val personId: String,
    @SerializedName("plate_number")
    val plateNumber: String,
    @SerializedName("russia_number")
    val russiaNumber: Boolean,
    @SerializedName("service_id")
    val serviceId: String
) : Parcelable