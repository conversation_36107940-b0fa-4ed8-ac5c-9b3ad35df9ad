package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcItemService(
    @SerializedName("id")
    val id: Int,

    @SerializedName("description")
    val description: String?,

    @SerializedName("name")
    val name: String?,
    @SerializedName("objectUuid")
    val objectUuid: MfcObjectUuid?
) : Parcelable