package lc.deck.rudn.entity.news


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class NewsCategoryLang(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
//    @SerializedName("news_category_id")
//    val newsCategoryId: Int?,
//    @SerializedName("lang_id")
//    val langId: Int?,
//    @SerializedName("website_id")
//    val websiteId: Int?,
//    @SerializedName("path")
//    val path: String?,
    @SerializedName("newsCategory")
    val newsCategory: NewsCategory?
): Parcelable