package lc.deck.rudn.entity.payment.invoice


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AccountPeriod(
    @SerializedName("currency")
    val currency: CurrencyX?,
    @SerializedName("date_begin")
    val dateBegin: String?,
    @SerializedName("date_end")
    val dateEnd: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("is_fine")
    val isFine: Int?,
    @SerializedName("sum")
    val sum: Double?
) : Parcelable