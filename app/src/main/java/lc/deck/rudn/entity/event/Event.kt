package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class Event(
    @SerializedName("id")
    val id: Int,
//    @SerializedName("event_activity_weekday_id")
//    val eventActivityWeekdayId: Int?,
//    @SerializedName("comment")
//    val comment: String?,
    @SerializedName("event_type_id")
    val eventTypeId: Int?,
//    @SerializedName("street")
//    val street: String?,
//    @SerializedName("house_number")
//    val houseNumber: String?,
//    @SerializedName("updated_at")
//    val updatedAt: String?,
//    @SerializedName("created_at")
//    val createdAt: String?,
//    @SerializedName("event_work_program_section_id")
//    val eventWorkProgramSectionId: Int?,
    @SerializedName("datetime_start")
    val datetimeStart: Date?,
    @SerializedName("datetime_end")
    val datetimeEnd: Date?,
//    @SerializedName("room_id")
//    val roomId: Int?,
    @SerializedName("eventType")
    val eventType: EventType?,
    @SerializedName("is_current")
    val isCurrent: Boolean?,
    @SerializedName("street")
    val street: String?,
    @SerializedName("is_favorite")
    val isFavorite: Boolean?,
    @SerializedName("is_past")
    val isPast: Boolean?,
    @SerializedName("eventService")
    val mfcEvent: MfcEvent?,
    @SerializedName("eventActivity")
    val eventActivity: EventActivity?,
    @SerializedName("eventWorkProgramSection")
    val eventWorkProgramSection: EventWorkProgramSection?,
//    @SerializedName("personEventFavorite")
//    val personEventFavorite: PersonEventFavorite?,
    @SerializedName("isVisited")
    val isVisited: Boolean?,
    @SerializedName("room")
    val room: Room?,
    @SerializedName("countParentComments")
    val countParentComments: Int?,
    @SerializedName("countOfVisitorsActivity")
    val countOfVisitorsActivity: CountOfVisitorsActivity?,
    @SerializedName("count_new_comments")
    val countNewComments: Int?,
    @SerializedName("datetime_fake")
    val fakeDatetime: Date?,
    @SerializedName("is_noted_all")
    val isNotedAll: Boolean?,

    var isLast: Boolean?
): Parcelable {
    fun eventName(): String? {
        if (eventTypeId == EventType.EVENT_TYPE_LESSON)
            return eventWorkProgramSection?.workProgramSection?.workProgram?.subject?.name

        if (eventTypeId == EventType.EVENT_TYPE_ACTION)
            return eventActivity?.activity?.name

        return null
    }

    fun teacherName(): String? {
        if (eventTypeId == EventType.EVENT_TYPE_LESSON) {
//            val name = eventWorkProgramSection?.eventWorkProgramSectionEmployees?.user?.person?.nameRus
//            val surname = eventWorkProgramSection?.eventWorkProgramSectionEmployees?.user?.person?.surnameRus
//            val patronomyc = eventWorkProgramSection?.eventWorkProgramSectionEmployees?.user?.person?.patronymicRus

//            if (surname == null) return null
//
//            var fullName = ""
//            fullName += surname
//            if (!name.isNullOrEmpty()) {
//                fullName += " ${name.first()}."
//            }
//            if (!patronomyc.isNullOrEmpty()) {
//                fullName += " ${patronomyc.first()}."
//            }
//
//            return fullName
        }

        return null
    }

    fun address(): String? {
        if (eventTypeId == EventType.EVENT_TYPE_LESSON)
            return listOfNotNull(room?.territory?.name, room?.name).joinToString(", ")
        if (eventTypeId == EventType.EVENT_TYPE_ACTION)
            return eventActivity?.activity?.place ?: eventActivity?.activity?.placeReal
        if (eventTypeId == EventType.EVENT_TYPE_MFC)
            return street
        return null
    }

    fun format(): String? {
        if (eventTypeId == EventType.EVENT_TYPE_LESSON)
            return eventWorkProgramSection?.workProgramSection?.workProgram?.workshopTypeParent?.name
        if (eventTypeId == EventType.EVENT_TYPE_ACTION)
            return actionFormat()
        return null
    }

    private fun actionFormat() = with(StringBuilder()) {
        eventActivity?.activity?.activityFormat?.name?.let {
            append("$it. ")
        }

        eventActivity?.activity?.activityDirectionParents?.forEach { directionParent ->
            if (!directionParent.name.isNullOrBlank()) {
                append("${directionParent.name}, ")
            }
        }

        eventActivity?.activity?.activityDirections?.forEach { direction ->
            if (!direction.name.isNullOrBlank()) {
                append("${direction.name}, ")
            }
        }
        if (this.endsWith(", ")) {
            delete(length - 2, length)
        }
        toString()
    }

    fun contactPersonName(): String? =
        eventActivity?.activity?.activityContactPersonRudn?.fio

    fun contactPersonEmail(): String? =
        eventActivity?.activity?.activityContactPersonRudn?.email

    fun sponsorName(): String? =
        eventActivity?.activity?.activityOrganizer?.name

    fun description(): String? {
        if (eventTypeId == EventType.EVENT_TYPE_LESSON)
            return eventWorkProgramSection?.workProgramSection?.description
        if (eventTypeId == EventType.EVENT_TYPE_ACTION)
            return eventActivity?.activity?.preview
        return null
    }

    fun isRunning(): Boolean {
        val now = Date()
        return datetimeStart?.before(now) == true && datetimeEnd?.after(now) == true
    }

    companion object{
        const val EVENT_TYPE_ID_1 = 1
        const val EVENT_TYPE_ID_2 = 2
    }
}