package lc.deck.rudn.entity.enrollee.personal_data

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.enrollee.enrollee.Documents
import lc.deck.rudn.entity.enrollee.enrollee.LegalRepresentative

@Parcelize
data class EnrolleeData(
    @SerializedName("documents")
    var documents: Documents?,
    @SerializedName("guid")
    var guid: String?,
    @SerializedName("legalRepresentative")
    var legalRepresentative: LegalRepresentative?,
    @SerializedName("personalData")
    var personalData: EnrolleePersonalData?,
    @SerializedName("placeOfBirth")
    var placeOfBirth: String?,
    @SerializedName("address_registration")
    var addressRegistration: String?,
    @SerializedName("address")
    var address: String?
) : Parcelable
