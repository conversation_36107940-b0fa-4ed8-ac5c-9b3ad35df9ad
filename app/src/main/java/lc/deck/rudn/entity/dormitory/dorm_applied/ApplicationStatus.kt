package lc.deck.rudn.entity.dormitory.dorm_applied


import android.os.Parcelable
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ApplicationStatus(
    @SerialName("description")
    val description: String?,
    @SerialName("guid")
    val guid: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("step")
    val step: String?
)