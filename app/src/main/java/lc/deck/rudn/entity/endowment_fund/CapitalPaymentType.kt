package lc.deck.rudn.entity.endowment_fund

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CapitalPaymentType(
    @SerializedName("title")
    val title : String?,
    @SerializedName("description")
    val description : String?,
    @SerializedName("formation_period")
    val formationPeriod : String?,
    @SerializedName("payment_type")
    val paymentType : List<PaymentType>?
) : Parcelable
