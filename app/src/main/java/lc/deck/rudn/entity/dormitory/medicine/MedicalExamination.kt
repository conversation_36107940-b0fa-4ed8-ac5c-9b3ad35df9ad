package lc.deck.rudn.entity.dormitory.medicine


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class MedicalExamination(
    @SerialName("agreementPhoneNumber")
    val agreementPhoneNumber: String?,
    @SerialName("billNumber")
    val billNumber: String?,
    @SerialName("clinicAddress")
    val clinicAddress: String?,
    @SerialName("description")
    val description: String?,
    @SerialName("examinations")
    val examinations: List<Examination>?,
    @SerialName("id")
    val id: String?,
    @SerialName("instructionFile")
    val instructionFile: InstructionFile?,
    @SerialName("isAgreementSigned")
    val isAgreementSigned: Boolean?,
    @SerialName("issueDate")
    val issueDate: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("number")
    val number: String?,
    @SerialName("recordDate")
    val recordDate: String?,
    @SerialName("status")
    val status: StatusX?
)