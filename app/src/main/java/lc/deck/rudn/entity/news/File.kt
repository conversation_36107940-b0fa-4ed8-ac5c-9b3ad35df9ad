package lc.deck.rudn.entity.news


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class File(
//    @SerializedName("id")
//    val id: Int?,
    @SerializedName("path")
    val path: String?
//    @SerializedName("hash")
//    val hash: String?,
//    @SerializedName("url")
//    val url: String?,
//    @SerializedName("user_id")
//    val userId: Int?,
//    @SerializedName("created_at")
//    val createdAt: Date?
): Parcelable