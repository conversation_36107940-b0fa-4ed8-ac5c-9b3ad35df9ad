package lc.deck.rudn.entity.enrollee.personal_data

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.enrollee.enrollee.Documents
import lc.deck.rudn.entity.enrollee.enrollee.LegalRepresentative

@Parcelize
data class PhoneNumbers(
    @SerializedName("role_id")
    var roleId: Int?,
    @SerializedName("phoneNumbers")
    var phoneNumbers: List<String>?,
) : Parcelable