package lc.deck.rudn.entity.applicant.documents


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class File(
    @SerializedName("base64")
    val base64: String?,
    @SerializedName("format")
    val format: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("size")
    val size: String?
) : Parcelable