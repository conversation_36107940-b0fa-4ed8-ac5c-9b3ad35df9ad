package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 10.02.2022, четверг
 **/
@Parcelize
data class PeriodSend(
    @SerializedName("date_start")
    val dateStart: String?,
    @SerializedName("date_end")
    val dateEnd: String?,
    @SerializedName("nomenclature_guid")
    val nomenclature: String?,
) : Parcelable