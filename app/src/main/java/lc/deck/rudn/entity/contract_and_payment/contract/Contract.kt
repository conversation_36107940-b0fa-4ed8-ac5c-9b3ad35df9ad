package lc.deck.rudn.entity.contract_and_payment.contract


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class Contract(
    @SerializedName("billing_center")
    val billingCenter: String?,
    @SerializedName("content")
    val content: String?,
    @SerializedName("contract_description")
    val contractDescription: String?,
    @SerializedName("contract_number")
    val contractNumber: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("end_date")
    val endDate: String?,
    @SerializedName("invoice_date")
    val invoiceDate: String?,
    @SerializedName("invoice_number")
    val invoiceNumber: String?,
    @SerializedName("paid")
    val paid: Boolean?,
    @SerializedName("payer")
    val payer: String?,
    @SerializedName("price")
    val price: String?,
    @SerializedName("purpose")
    val purpose: String?,
    @SerializedName("start_date")
    val startDate: String?,
    @SerializedName("type")
    val type: String?,
    @SerializedName("сounterparty")
    val counterparty: String?,
) : Parcelable