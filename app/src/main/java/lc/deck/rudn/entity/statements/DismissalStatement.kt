package lc.deck.rudn.entity.statements

import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 0007 и 0008 увольнение по собственному
 */
@Parcelize
data class DismissalStatement(
    @SerializedName("applicationDate")
    override var applicationDate: String = "",
    @SerializedName("jobTitle")
    override var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    override var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    override var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    override var rate: Double = 0.0,
    @SerializedName("attachments")
    override var attachments: MutableList<Attachment> = mutableListOf(),
    @SerializedName("reasoningID")
    val reasoningID: String? = null,
    @SerializedName("reasoning")
    val reasoning: String = "",
    @SerializedName("electronicDocument")
    val electronicDocument: Boolean? = null,
) : Statement() {

    fun toRequest() = DismissalStatementRequest(
        applicationDate,
        jobTitle,
        subdivision,
        headquarter,
        rate,
        attachments,
        reasoningID,
        reasoning,
        electronicDocument
    )
}

data class DismissalStatementRequest(
    @SerializedName("applicationDate")
    val applicationDate: String = "",
    @SerializedName("jobTitle")
    val jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    val subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    val headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    val rate: Double = 0.0,
    @SerializedName("attachments")
    val attachments: List<Attachment> = emptyList(),
    @SerializedName("reasoningID")
    val reasoningID: String? = null,
    @SerializedName("reasoning")
    val reasoning: String = "",
    @SerializedName("electronicDocument")
    val electronicDocument: Boolean? = null,
)
