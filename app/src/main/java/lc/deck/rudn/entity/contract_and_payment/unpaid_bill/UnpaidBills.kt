package lc.deck.rudn.entity.contract_and_payment.unpaid_bill

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.contract_and_payment.contract.Contract
import lc.deck.rudn.entity.contract_and_payment.unpaid_bill.UnpaidBill

@Parcelize
data class UnpaidBills(
    @SerializedName("date_from")
    val dateFrom: String,
    @SerializedName("date_to")
    val dateTo: String,
    @SerializedName("bills")
    val unpaidBills: List<UnpaidBill?>?,
) : Parcelable