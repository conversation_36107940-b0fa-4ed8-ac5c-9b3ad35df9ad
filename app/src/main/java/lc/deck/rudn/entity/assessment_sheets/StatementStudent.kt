package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 18.05.2022, среда
 **/
@Parcelize
data class StatementStudent(
    @SerializedName("statement_id")
    val statement_id: Int?,
    @SerializedName("semestr_result")
    val semesterResult: Int?,
    @SerializedName("certification_result")
    val certificationResult: Int?,
    @SerializedName("is_absent")
    val isAbsent: Boolean?,
    @SerializedName("topic")
    val topic: String?,
    @SerializedName("topic_eng")
    val topicEng: String?,
) : Parcelable
