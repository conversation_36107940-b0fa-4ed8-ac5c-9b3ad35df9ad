package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FinishedDocuments(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("extension")
    val extension: String?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("weight")
    val weight: Long?,
    @SerializedName("need_signature")
    val needSignature: Boolean?
) : Parcelable