package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcCreation(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String?,
    @SerializedName("ready_time_day")
    val ready_time_day: String?,
    @SerializedName("price")
    val price: Int?,
    @SerializedName("serviceDocuments")
    val serviceDocuments: List<MfcServiceDocument>,
    @SerializedName("reference")
    val reference: MfcReference?,
    @SerializedName("serviceType")
    val serviceType: MfcServiceType?,
    @SerializedName("is_need_to_pay")
    val isNeedToPay: Boolean,
    @SerializedName("is_order_available")
    val isOrderAvailable: Boolean?,
    @SerializedName("is_calendar_ready_time")
    val isCalendarReadyTime: Boolean?,
    @SerializedName("ready_time")
    val readyTime: Int?,
    @SerializedName("application")
    val application: Application?,
    @SerializedName("need_contract")
    val needContract: Boolean?,
    @SerializedName("need_date")
    val needDate: Boolean?,
    @SerializedName("reason_call")
    val reasonCall: Boolean?,
    @SerializedName("applicant_category")
    val applicantCategory: Boolean?,
    @SerializedName("periodParams")
    val periodParams: PeriodParams?,
    @SerializedName("need_document_signature")
    val needDocumentSignature: Boolean?,
    @SerializedName("need_payment")
    val needPayment: Boolean?,
    @SerializedName("need_position")
    val needPosition : Boolean?,
) : Parcelable {
    companion object {
        const val ARG_MAKE_DAYS = 1440
    }
}