package lc.deck.rudn.entity.personnel_transactions


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class File(
    @SerializedName("format")
    val format: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("signature")
    val signature: Signature?,
    @SerializedName("size")
    val size: String?,
    @SerializedName("url")
    val url: String? = null,
    @SerializedName("base64")
    val base64: String? = null,
) : Parcelable