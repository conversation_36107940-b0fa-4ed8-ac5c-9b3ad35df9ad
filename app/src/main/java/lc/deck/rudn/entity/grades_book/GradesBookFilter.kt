package lc.deck.rudn.entity.grades_book

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class GradesBookFilter(
    @SerializedName("academ_year")
    val academYear: AcademYear,
    @SerializedName("semestr")
    val semester: Semester,
    @SerializedName("final_control_form_types")
    val finalControlForm: List<FinalControlForm>,
    @SerializedName("course")
    val course: Course?,
    @SerializedName("has_final_examination")
    val isFinalExamination: <PERSON><PERSON><PERSON>,
    @SerializedName("is_current")
    val isCurrent: <PERSON>olean
) : Parcelable