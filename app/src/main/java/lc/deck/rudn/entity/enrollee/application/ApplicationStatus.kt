package lc.deck.rudn.entity.enrollee.application


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ApplicationStatus(
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("next_button_text")
    val nextButtonText: String?
) : Parcelable {
    companion object {
        const val GUID_12 = "12"
        const val GUID_14 = "14"
    }
}