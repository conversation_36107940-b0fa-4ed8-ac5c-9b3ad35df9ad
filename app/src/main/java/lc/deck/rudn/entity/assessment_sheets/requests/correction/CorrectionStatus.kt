package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CorrectionStatus(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("correctionStatusType")
    val correctionStatusType: CorrectionStatusType?
):Parcelable{
    companion object {
        const val STATUS_1 = 1
        const val STATUS_2 = 2
    }
}