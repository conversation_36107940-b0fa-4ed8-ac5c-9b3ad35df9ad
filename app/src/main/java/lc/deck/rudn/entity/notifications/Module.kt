package lc.deck.rudn.entity.notifications


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Module(
    @SerializedName("id")
    val id: Int?, // 2
    @SerializedName("name")
    val name: String? // comments
): Parcelable {
    companion object {
        const val MODULE_MFC = 1
        const val MODULE_COMMENT = 2
        const val MODULE_EVENT = 3
    }
}