package lc.deck.rudn.entity.fok.tickets


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Tickets(
    @SerialName("additionalInfo")
    val additionalInfo: List<AdditionalInfo?>?,
    @SerialName("address")
    val address: String?,
    @SerialName("complexName")
    val complexName: String?,
    @SerialName("currency")
    val currency: String?,
    @SerialName("id")
    val id: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("paymentID")
    val paymentID: String?,
    @SerialName("price")
    val price: Int?,
    @SerialName("qr")
    val qr: String?,
    @SerialName("service")
    val service: Service?,
    @SerialName("status")
    val status: Status?,
    @SerialName("tag")
    val tag: List<String?>?,
    @SerialName("ticketCreationDate")
    val ticketCreationDate: String?,
    @SerialName("ticketNumber")
    val ticketNumber: String?
) : java.io.Serializable