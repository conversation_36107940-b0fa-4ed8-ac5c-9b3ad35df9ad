package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentContract(
    @SerializedName("contractPeriods")
    val contractPeriods: List<ContractPeriod>?,
    @SerializedName("contract_type")
    val contractType: ContractType?,
    @SerializedName("date_begin")
    val dateBegin: String?,
    @SerializedName("date_end")
    val dateEnd: String?,
    @SerializedName("debt")
    val debt: String?,
    @SerializedName("edu_level")
    val eduLevel: String?,
    @SerializedName("edu_program")
    val eduProgram: String?,
    @SerializedName("form_edu")
    val formEdu: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("hostel_place")
    val hostelPlace: String?,
    @SerializedName("is_closed")
    val isClosed: Boolean?,
    @SerializedName("is_rub")
    val isRub: Boolean?,
    @SerializedName("issue_date")
    val issueDate: String?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("year_begin")
    val yearBegin: String?,
    @SerializedName("currency")
    val currency: Currency?
) : Parcelable {
    companion object {
        const val CLOSED = true
        const val NOT_CLOSED = false
        const val RUBBLE = true
        const val NOT_RUBBLE = false

    }
}