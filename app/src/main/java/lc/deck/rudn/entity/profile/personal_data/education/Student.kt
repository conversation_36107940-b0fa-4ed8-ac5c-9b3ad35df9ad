package lc.deck.rudn.entity.profile.personal_data.education

import lc.deck.rudn.entity.employee.PersonEmail
import lc.deck.rudn.entity.enrollee.enrollee.Citizenship

data class Student(
    //название группы
    val academGroup: String?,
    //гражданство
    val citizenship: String?,
    //дата рождения
    val birthday: String?,
    //email
    val emails: List<PersonEmail>?,
    //номер студ билета
    val userNld: String?,
    //уровень образования
    val educationLevel: String?,
    //курс
    val course: String?,
    //учебное подразделение
    val academGroupFacultyName: String?,
    //направление
    val educationParentProgram: String?,
    //Образовательная программа
    val educationParentProfile: String?,
    //форма обучения
    val educationForm: String?,
    //номер приказа зачисления
    val admissionOrderNumber: String?,
    //дата приказа зачисления
    val admissionOrderDate: String?
)
