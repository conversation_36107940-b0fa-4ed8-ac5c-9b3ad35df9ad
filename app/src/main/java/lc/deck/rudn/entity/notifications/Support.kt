package lc.deck.rudn.entity.notifications

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Support(

    @SerializedName("status_id")
	val statusId: Int? = null,

	@SerializedName("incident")
	val incident: String? = null
) : Parcelable {

    companion object {
        const val STATUS_IN_WORK = 1
        const val STATUS_FINISHED = 2
    }
}
