package lc.deck.rudn.entity.statements

import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 0003 Постоянный перевод на дистанционку
 */
@Parcelize
data class RemotePermanentStatement(
    @SerializedName("applicationDate")
    override var applicationDate: String = "",
    @SerializedName("jobTitle")
    override var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    override var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    override var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    override var rate: Double = 0.0,
    @SerializedName("attachments")
    override var attachments: MutableList<Attachment> = mutableListOf(),
    @SerializedName("reasoning")
    var reasoning: String = "",
    @SerializedName("continuously")
    val continuously: Boolean = false,
    @SerializedName("schedule")
    val schedule: Schedule = Schedule(),
) : Statement() {

    fun toRequest() = RemotePermanentStatementRequest(
        applicationDate, jobTitle, subdivision, headquarter, rate, attachments, reasoning, continuously, schedule
    )
}

data class RemotePermanentStatementRequest(
    @SerializedName("applicationDate")
    val applicationDate: String = "",
    @SerializedName("jobTitle")
    val jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    val subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    val headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    val rate: Double = 0.0,
    @SerializedName("attachments")
    var attachments: List<Attachment> = emptyList(),
    @SerializedName("reasoning")
    var reasoning: String = "",
    @SerializedName("continuously")
    val continuously: Boolean = false,
    @SerializedName("schedule")
    val schedule: Schedule = Schedule(),
)
