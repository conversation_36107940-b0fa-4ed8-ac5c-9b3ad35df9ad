package lc.deck.rudn.entity.applicant.documents


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Document(
    @SerializedName("format")
    val format: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("size")
    val size: String?,
    @SerializedName("task_guid")
    val taskGuid: String?
) : Parcelable