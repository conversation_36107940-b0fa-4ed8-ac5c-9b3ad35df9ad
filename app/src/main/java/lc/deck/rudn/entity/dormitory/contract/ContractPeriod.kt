package lc.deck.rudn.entity.dormitory.contract


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ContractPeriod(
    @SerialName("course")
    val course: String?,
    @SerialName("currency")
    val currency: String?,
    @SerialName("end_date")
    val endDate: String?,
    @SerialName("group_by")
    val groupBy: String?,
    @SerialName("guid")
    val guid: String?,
    @SerialName("payment_date")
    val paymentDate: String?,
    @SerialName("start_date")
    val startDate: String?,
    @SerialName("sum")
    val sum: String?,
    @SerialName("year")
    val year: String?
)