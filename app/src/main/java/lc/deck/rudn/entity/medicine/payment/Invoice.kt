package lc.deck.rudn.entity.medicine.payment

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Invoice(
    @SerialName("accountPeriods")
    val accountPeriods: List<AccountPeriod>,
    @SerialName("currency")
    val currency: CurrencyX,
    @SerialName("number")
    val number: String,
    @SerialName("service")
    val service: ServiceX,
    @SerialName("sum")
    val sum: Double
)