package lc.deck.rudn.entity.payment.operation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.payment.contract.Currency

@Parcelize
data class AccountPeriod(
    @SerializedName("currency")
    val currency: Currency?,
    @SerializedName("date_begin")
    val dateBegin: String?,
    @SerializedName("date_end")
    val dateEnd: String?,
    @SerializedName("is_fine")
    val isFine: Int?,
    @SerializedName("sum")
    val sum: Int?
) : Parcelable{
    companion object{
        const val FINE = 1
        const val NOT_FINE = 0
    }
}