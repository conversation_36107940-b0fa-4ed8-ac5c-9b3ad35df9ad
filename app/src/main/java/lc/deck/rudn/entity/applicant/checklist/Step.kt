package lc.deck.rudn.entity.applicant.checklist


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Step(
    @SerializedName("actionStatus")
    val actionStatus: ActionStatus?,
    @SerializedName("button")
    val button: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("guid")
    val guid: Int?,
    @SerializedName("order")
    val order: Int?,
    @SerializedName("stepStatus")
    val stepStatus: StepStatus?,
    @SerializedName("title")
    val title: String?
) : Parcelable