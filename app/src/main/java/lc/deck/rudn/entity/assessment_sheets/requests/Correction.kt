package lc.deck.rudn.entity.assessment_sheets.requests


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.assessment_sheets.Person
import lc.deck.rudn.entity.assessment_sheets.Statement
import lc.deck.rudn.entity.assessment_sheets.requests.correction.CorrectionState
import lc.deck.rudn.entity.assessment_sheets.requests.correction.CorrectionStatus
import lc.deck.rudn.entity.assessment_sheets.requests.correction.CorrectionStudents
import lc.deck.rudn.entity.assessment_sheets.requests.rejection.RejectionStatus
import lc.deck.rudn.entity.assessment_sheets.requests.rejection.StatementRejectPerson
import java.util.*

@Parcelize
data class Correction(
    @SerializedName("correctionState")
    val correctionState: CorrectionState?,
    @SerializedName("correctionStatus")
    val correctionStatus: CorrectionStatus?,
    @SerializedName("statementRejectStatus")
    val rejectStatus: RejectionStatus?,
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("person")
    val person: Person?,
    @SerializedName("statement")
    val statement: Statement?,
    @SerializedName("correctionStudents")
    val correctionStudents: List<CorrectionStudents?>?,
    @SerializedName("decision_students")
    val decisionStudents: Int?,
    @SerializedName("total_students")
    val totalStudents: Int?,
    @SerializedName("comment")
    val comment: String?,
    @SerializedName( "statementRejectPersons")
    val statementRejectPersons: List<StatementRejectPerson?>? = null,
    @SerializedName("is_changed")
    val isChanged: Boolean?
) : Parcelable