package lc.deck.rudn.entity.profile.personal_data

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FullDataEducation(
    @SerialName("count")
    val count: Int?,
    @SerialName("date_of_issue")
    val dateOfIssue: String?,
    @SerialName("document_type")
    val documentType: String?,
    @SerialName("educational_institution")
    val educationalInstitution: String?,
    @SerialName("end")
    val end: String?,
    @SerialName("name_course")
    val nameCourse: String?,
    @SerialName("number")
    val number: String?,
    @SerialName("place")
    val place: String?,
    @SerialName("qualification")
    val qualification: String?,
    @SerialName("series")
    val series: String?,
    @SerialName("speciality")
    val speciality: String?,
    @SerialName("start")
    val start: String?,
    @SerialName("type_education")
    val typeEducation: String?
)