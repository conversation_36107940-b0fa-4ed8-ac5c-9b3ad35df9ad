package lc.deck.rudn.entity.medicine.payment

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Bank(
    @SerializedName("image")
    val image: String? = null,
    @SerializedName("payment_url")
    val paymentUrl: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("id")
    val id: Int? = null
): Parcelable