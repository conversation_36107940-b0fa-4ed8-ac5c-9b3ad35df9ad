package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractPeriod(
    @SerializedName("class")
    val classX: String?,
    @SerializedName("currency")
    val currency: Currency?,
    @SerializedName("date_end")
    val dateEnd: String?,
    @SerializedName("date_start")
    val dateStart: String?,
    @SerializedName("days_left")
    val daysLeft: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("is_debt")
    val isDebt: Boolean?,
    @SerializedName("is_list")
    val isList: Boolean?,
    @SerializedName("is_reminder")
    val isReminder: Boolean?,
    @SerializedName("nomenclature")
    val nomenclature: String?,
    @SerializedName("paid_desc")
    val paidDesc: PaidDesc?,
    @SerializedName("plan_payment_date")
    val planPaymentDate: String?,
    @SerializedName("sum")
    val sum: Number?,
    @SerializedName("year")
    val year: String?

) : Parcelable {
    companion object {
        const val HAS_DEBT = true
        const val NO_DEBT = false
        const val HAS_REMAINDER = true
        const val NO_REMAINDER = false
        const val HAS_LIST = true
    }
}