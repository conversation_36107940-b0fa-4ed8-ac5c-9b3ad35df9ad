package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.assessment_sheets.Mark
import lc.deck.rudn.entity.assessment_sheets.MarkEcts

@Parcelize
data class CorrectionStudentResult(
    @SerializedName("certification_result")
    var certificationResult: Int?,
    @SerializedName("is_absent")
    var isAbsent: Boolean?,
    @SerializedName("is_mistake")
    val isMistake: Boolean?,
    @SerializedName("mark")
    val mark: Mark?,
    @SerializedName("markEcts")
    val markEcts: MarkEcts?,
    @SerializedName("semestr_result")
    var semestrResult: Int?,
    @SerializedName("topic")
    val topic: String?,
    @SerializedName("topic_eng")
    val topicEng: String?,
    @SerializedName("total_result")
    val totalResult: Int?
):Parcelable