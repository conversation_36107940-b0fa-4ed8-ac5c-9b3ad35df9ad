package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class StatementStatus(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
) : Parcelable {
    companion object {
        const val STATUS_1 = 1
        const val STATUS_2 = 2
        const val STATUS_4 = 4
        const val STATUS_6 = 6
        const val STATUS_8 = 8
        const val STATUS_9 = 9
    }
}