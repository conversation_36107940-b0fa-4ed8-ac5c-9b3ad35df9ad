package lc.deck.rudn.entity.endowment_fund

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CapitalPaymentDetail(
    @SerializedName("capital_name")
    val capitalName : String?,
    @SerializedName("file_id")
    val fileId : Int?,
    @SerializedName("paymentDetails")
    val paymentDetails : List<PaymentDetails?>?
) : Parcelable
