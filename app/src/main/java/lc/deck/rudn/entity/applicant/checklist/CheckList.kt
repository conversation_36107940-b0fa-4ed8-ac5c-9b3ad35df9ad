package lc.deck.rudn.entity.applicant.checklist


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CheckList(
    @SerializedName("applicant_status_id")
    val applicantStatusId: Int?,
    @SerializedName("notification")
    val notification: String?,
    @SerializedName("steps")
    val steps: List<Step>?
) : Parcelable