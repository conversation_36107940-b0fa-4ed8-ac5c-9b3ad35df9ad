package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcReferenceOrder(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("is_paper")
    val isPaper: Boolean?,
    @SerializedName("is_ecp")
    val isDigital: Boolean?,
    @SerializedName("count")
    val count: Int?,
    @SerializedName("educationProgramParent")
    val eduProgram: SingleSelectionItem,
    @SerializedName("fio_latin")
    val fioLatin: String?,
    @SerializedName("fio_child")
    val fioChild: String?,
    @SerializedName("child_birthday")
    val childBirthday: String?,
    @SerializedName("childBirthdayType")
    val childBirthdayType: SingleSelectionItem?,
    @SerializedName("graduateIntern")
    val graduateOrIntern: SingleSelectionItem?,
    @SerializedName("docType")
    val docType: SingleSelectionItem?,
    @SerializedName("admissionOrder")
    val admissionOrder: AdmissionOrder?,
    @SerializedName("place_of_demand")
    val demand: String?,
    @SerializedName("is_dismission")
    val isDismission: Boolean?,
    @SerializedName("referenceOrderAcademYears")
    val referenceOrderAcademYears: List<MfcAcademYear>?,
    @SerializedName("duplicate_reason")
    val duplicateReason: DuplicateReason,
    @SerializedName("period_start")
    val periodStart: String?,
    @SerializedName("period_end")
    val periodEnd: String?,
    @SerializedName("calendar_year")
    val calendarYear: String?,
    @SerializedName("mvd")
    val mvd: SingleSelectionItem?,
    @SerializedName("military_document_type")
    val militaryDocumentType: String?,
    @SerializedName("stock_categories")
    val stockCategories: String?,
    @SerializedName("military_personnel")
    val militaryPersonnel: String?,
    @SerializedName("military_rank")
    val militaryRank: String?,
    @SerializedName("category_of_fitness")
    val categoryOfFitness: String?,
    @SerializedName("military_enlistment_office")
    val militaryEnlistmentOffice: String?,
    @SerializedName("attitudes_military_service")
    val attitudesMilitaryService: String?,
    @SerializedName("number_vus")
    val numberVus: String?,
    @SerializedName("military_document_series")
    val militaryDocumentSeries: String?,
    @SerializedName("military_document_number")
    val militaryDocumentNumber: String?,
    @SerializedName("name_education")
    val nameEducation: String?,
    @SerializedName("name_education_specialty")
    val nameEducationSpecialty: String?,
    @SerializedName("reasonBudget")
    val reasonBudget: DuplicateReason?,
    @SerializedName("visit_time")
    val visitTime: String?,
    @SerializedName("faculty_archive")
    val facultyArchive: String?,
    @SerializedName("nld_archive")
    val nldArchive: String?,
    @SerializedName("speciality_archive")
    val specialityArchive: String?,
    @SerializedName("year_archive")
    val yearArchive: String?,
    @SerializedName("archiveVisitReason")
    val archiveVisitReason: DuplicateReason?,
    @SerializedName("reasonShiftFio")
    val reasonShiftFio: DuplicateReason?,
    @SerializedName("achievementType")
    val achievementType: DuplicateReason?,
    @SerializedName("achievementCategory")
    val achievementCategory: DuplicateReason?,
    @SerializedName("achievementLevel")
    val achievementLevel: DuplicateReason?,
    @SerializedName("onContract")
    val onContract: Contract?,
    @SerializedName("semester")
    val semester: DuplicateReason?,
    @SerializedName("reasonTransfer")
    val reasonTransfer: DuplicateReason?,
    @SerializedName("currency")
    val currency: DuplicateReason?,
    @SerializedName("academicYear")
    val academicYear: DuplicateReason?,

) : Parcelable