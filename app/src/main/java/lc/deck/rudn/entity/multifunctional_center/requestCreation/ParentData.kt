package lc.deck.rudn.entity.multifunctional_center.requestCreation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ParentData(

    @SerializedName("residential_address")
    val residentialAddress: String? = null,

    @SerializedName("place_of_work")
    val placeOfWork: String? = null,

    @SerializedName("fio")
    val fio: String? = null
) : Parcelable