package lc.deck.rudn.entity.multifunctional_center.requestCreation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import kotlinx.android.parcel.RawValue
import okhttp3.RequestBody

@Parcelize
data class MfcRequestApplicationOrder(

    @SerializedName("application_id")
    val applicationId: Int?,

    @SerializedName("order_source_id")
    val orderSourceId: Int = 1,

    @SerializedName("free_input_field")
    val freeInputField: String? = null,

    @SerializedName("return_reason_id")
    val returnReasonId: Int? = null,

    @SerializedName("fatherData")
    val fatherData: ParentData? = null,

    @SerializedName("exam_scores")
    val examScores: Number? = null,

    @SerializedName("motherData")
    val motherData: ParentData? = null,

    @SerializedName("requisites")
    val requisites: Requisites? = null,

    @SerializedName("discount_reason_id")
    val discountReasonId: Int? = null,

    @SerializedName("payment_option_id")
    val paymentOptionId: Int? = null,

    @SerializedName("paymentSumDate")
    val paymentSumDate: HashMap<String, @RawValue RequestBody>? = null,

    @SerializedName("paymentSumSum")
    val paymentSumSum: HashMap<String, Number>? = null,

    @SerializedName("education_programm_type")
    val educationProgrammTypeId: Int? = null,

    @SerializedName("education_programm_benefits")
    val educationProgramBenefitId: Int? = null,

    @SerializedName("date_agenda")
    val dateAgenda: String? = null,

    ) : Parcelable
