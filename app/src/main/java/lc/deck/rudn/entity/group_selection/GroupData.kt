package lc.deck.rudn.entity.group_selection


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class GroupData(
    @SerializedName("undergraduateAcademGroups")
    val undergraduateAcademGroupsList: List<StudentGroup>?,
    @SerializedName("showGroupSelectorInSettings")
    val showGroupSelectorInSettings: Boolean?,
    @SerializedName("showGroupSelectorOnAuth")
    val showGroupSelectorOnAuth: Boolean?
): Parcelable