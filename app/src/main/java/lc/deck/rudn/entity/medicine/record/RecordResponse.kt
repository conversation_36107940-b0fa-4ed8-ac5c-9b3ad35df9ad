package lc.deck.rudn.entity.medicine.record

import kotlinx.serialization.SerialName

data class RecordResponse(

	@SerialName("plan_time")
	val planTime: String,

	@SerialName("account_number")
	val accountNumber: String? = null,

	@SerialName("address")
	val address: String? = null,

	@SerialName("place")
	val place: String? = null,

	@SerialName("documents")
	val documents: List<Document>? = null,

	@SerialName("service")
	val service: Service,

	@SerialName("is_payment")
	val isPayment: Boolean,

	@SerialName("ticket_number")
	val ticketNumber: String,

	@SerialName("application_guid")
	val applicationGuid: String? = null,

	@SerialName("plan_date")
	val planDate: String,

	@SerialName("room")
	val room: String? = null
)

