package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class WorkProgramSectionDocument(
//    @SerializedName("id")
//    val id: Int,
//    @SerializedName("work_program_section_id")
//    val workProgramSectionId: Int?,
//    @SerializedName("document_id")
//    val documentId: Int?,
//    @SerializedName("employee_id")
//    val employeeId: Int?,
    @SerializedName("document")
    val document: Document?
): Parcelable