package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcStatus(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("datetime")
    val date: String,
    @SerializedName("additionalInfo")
    val additionalInfo: MfcAdditionalInfo?,
    @SerializedName("comment")
    val comment: String?
) : Parcelable {
    companion object {
      const val  STATUS_1 = 1
      const val  STATUS_2 = 2
      const val  STATUS_3 = 3
      const val  STATUS_9 = 9
    }
}