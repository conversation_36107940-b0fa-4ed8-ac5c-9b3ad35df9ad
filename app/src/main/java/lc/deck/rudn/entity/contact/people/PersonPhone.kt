package lc.deck.rudn.entity.contact.people

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PersonPhone(

    @SerializedName("phoneType")
	val phoneType: PhoneType? = null,

    @SerializedName("phone_type_id")
	val phoneTypeId: Int? = null,

    @SerializedName("phone")
	val phone: String? = null,

    @SerializedName("description")
	val description: String? = null,

    @SerializedName("contact_person_id")
	val contactPersonId: Int? = null,

    @SerializedName("id")
	val id: Int? = null,

    var internalPhone: String = ""
): Parcelable
