package lc.deck.rudn.entity.enrollee.contract


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Contract(
    @SerializedName("address")
    val address: String?,
    @SerializedName("contract_guid")
    val contractGuid: String?,
    @SerializedName("contractPeriods")
    val contractPeriods: List<ContractPeriod>?,
    @SerializedName("contractStatus")
    val contractStatus: ContractStatus?,
    @SerializedName("contractType")
    val contractType: ContractType?,
    @SerializedName("documents")
    val documents: List<Document>?,
    @SerializedName("educationForm")
    val educationForm: EducationForm?,
    @SerializedName("educationLevel")
    val educationLevel: EducationLevel?,
    @SerializedName("educationProfile")
    val educationProfile: EducationProfile?,
    @SerializedName("educationProgram")
    val educationProgram: EducationProgram?,
    @SerializedName("end_date")
    val endDate: String?,
    @SerializedName("enrollee")
    val enrollee: Enrollee?,
    @SerializedName("faculty")
    val faculty: Faculty?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("payer")
    val payer: Payer?,
    @SerializedName("paymentType")
    val paymentType: PaymentType?,
    @SerializedName("room")
    val room: String?,
    @SerializedName("start_date")
    val startDate: String?,
    @SerializedName("sumPayment")
    val sumPayment: SumPayment?,
    @SerializedName("sumTotal")
    val sumTotal: SumTotal?,
    @SerializedName("year")
    val year: String?
) : Parcelable {
    companion object {
        const val CONTRACT_STUDY = "1"
        const val CONTRACT_DORM = "2"
    }
}