package lc.deck.rudn.entity.schedule


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Employee(
    @SerializedName("id")
    val id: String?,
    @SerializedName("name_rus")
    val nameRus: String?,
    @SerializedName("patronymic_rus")
    val patronymicRus: String?,
    @SerializedName("surname_rus")
    val surnameRus: String?
) : Parcelable