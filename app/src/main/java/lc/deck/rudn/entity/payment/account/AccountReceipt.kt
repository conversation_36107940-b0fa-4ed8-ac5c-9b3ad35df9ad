package lc.deck.rudn.entity.payment.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.payment.contract.Currency
import lc.deck.rudn.entity.payment.operation.AccountPeriod

@Parcelize
data class AccountReceipt(
    @SerializedName("QR")
    val QR: String?,
    @SerializedName("accountPeriods")
    val accountPeriods: List<AccountPeriod?>?,
    @SerializedName("account_url")
    val accountUrl: String?,
    @SerializedName("closure_date")
    val closureDate: String?,
    @SerializedName("currency")
    val currency: Currency?,
    @SerializedName("issue_date")
    val issueDate: String?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("sum")
    val sum: Double?
) : Parcelable