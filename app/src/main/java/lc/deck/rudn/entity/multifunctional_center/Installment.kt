package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Installment(
    @SerializedName("countSum")
    val countSum: List<CountSum?>?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("is_selected")
    var isSelected: Boolean? = false
) : Parcelable {
    companion object {
       const val CUSTOM_INSTALLMENT_ID = -1000
    }
}