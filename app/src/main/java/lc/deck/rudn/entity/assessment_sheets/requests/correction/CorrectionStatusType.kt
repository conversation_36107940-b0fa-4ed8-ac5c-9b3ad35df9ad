package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CorrectionStatusType(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
): Parcelable{
    companion object{
        const val  CORRECTIONS_STATUS_TYPE_1 = 1
        const val  CORRECTIONS_STATUS_TYPE_2 = 2
        const val  CORRECTIONS_STATUS_TYPE_3 = 3
    }
}