package lc.deck.rudn.entity.subject

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AcademYear(

	@SerializedName("is_deleted")
	val isDeleted: Boolean? = null,

	@SerializedName("id")
	val id: Int? = null,

	@SerializedName("object_id")
	val objectId: Int? = null,

	@SerializedName("value")
	val value: String? = null
) : Parcelable
