package lc.deck.rudn.entity.enrollee.contract_account


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractAccount(
    @SerializedName("account_number")
    val accountNumber: String?,
    @SerializedName("accountPeriods")
    val accountPeriods: List<AccountPeriod?>?,
    @SerializedName("account_qr")
    val accountQr: String?,
    @SerializedName("account_url")
    val accountUrl: String?,
    @SerializedName("banks")
    val banks: List<Bank?>?,
    @SerializedName("contract_issue_date")
    val contractIssueDate: String?,
    @SerializedName("contract_number")
    val contractNumber: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("description")
    val description: List<Description?>?,
    @SerializedName("issue_date")
    val issueDate: String?,
    @SerializedName("notification")
    val notification: String?,
    @SerializedName("sum_total")
    val sumTotal: Double?
) : Parcelable