package lc.deck.rudn.entity.enrollee.enrollee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class LegalRepresentative(
    @SerializedName("birth_date")
    var birthDate: String?,
    @SerializedName("email")
    var email: String?,
    @SerializedName("guid")
    var guid: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("patronymic")
    var patronymic: String?,
    @SerializedName("person_uuid")
    var personUuid: String?,
    @SerializedName("phone_number")
    var phoneNumber: String?,
    @SerializedName("representative_type")
    var representativeType: RepresentativeType?,
    @SerializedName("rudn_id")
    var rudnId: String?,
    @SerializedName("surname")
    var surname: String?,
    @SerializedName("documents")
    var documents: Documents?
) : Parcelable