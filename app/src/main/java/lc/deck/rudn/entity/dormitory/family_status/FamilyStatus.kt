package lc.deck.rudn.entity.dormitory.family_status

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FamilyStatus(
    @SerialName("status_guid")
    val statusGuid: String?,
    @SerialName("name")
    val name: String?
) {
    companion object {
        const val STATUS_FULL_FAMILY = "b28a1cfd-7d84-11ee-a2fb-00155d322400"
        const val STATUS_ONE_PARENT ="bdcf6064-7d84-11ee-a2fb-00155d322400"
        const val STATUS_GUARDIAN = "c53bbce7-7d84-11ee-a2fb-00155d322400"
        const val STATUS_ORGANIZATION ="cce87bd6-7d84-11ee-a2fb-00155d322400"
    }
}
