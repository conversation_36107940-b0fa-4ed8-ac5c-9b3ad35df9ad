package lc.deck.rudn.entity.performance


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.employee.Employee

@Parcelize
data class PerformanceSubject(
    @SerializedName("curriculumSubject")
    val curriculumSubject: CurriculumSubject?,
    @SerializedName("curriculum_subject_id")
    val curriculumSubjectId: Int?,
    @SerializedName("employee")
    val employee: Employee?,
    @SerializedName("employee_id")
    val employeeId: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("mark")
    val mark: Int?,
    @SerializedName("score_ects")
    val scoreEcts: String?,
    @SerializedName("score_trad")
    val scoreTrad: Int?,
    @SerializedName("undergraduate_academ_group_id")
    val undergraduateAcademGroupId: Int?
): Parcelable