package lc.deck.rudn.entity.personnel_transactions.agreement


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AgreementData(
    @SerializedName("agreement")
    val agreement: Agreement?,
    @SerializedName("is_signed")
    val isSigned: Boolean?,
    @SerializedName("status")
    val status: String?
) : Parcelable