package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CorrectionState(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
) : Parcelable {
    companion object {
        const val STATE_CORRECTION_NEW = 1
        const val STATE_CORRECTION_ARCHIVE = 2
        const val STATE_REJECTION_NEW = 1
        const val STATE_REJECTION_NEW_2 = 2
        const val STATE_REJECTION_ARCHIVE = 3
    }
}