package lc.deck.rudn.entity.contract_and_payment.payer


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PayerInfo(
    @SerializedName("email")
    val email: List<Email?>?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("patronymic")
    val patronymic: String?,
    @SerializedName("surname")
    val surname: String?
) : Parcelable