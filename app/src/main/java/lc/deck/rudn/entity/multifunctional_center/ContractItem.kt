package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractItem(
    @SerializedName("contractType")
    val contractType: ContractType?,
    @SerializedName("contract_uuid")
    val contractUuid: String?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
) : Parcelable