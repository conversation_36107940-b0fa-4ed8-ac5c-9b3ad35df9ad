package lc.deck.rudn.entity.enrollee.application


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PersonalDataStatusText(
    @SerializedName("legalRepresentativeTextPersonData")
    val legalRepresentativeTextPersonData: LegalRepresentativeTextPersonData?,
    @SerializedName("textPersonData")
    val textPersonData: TextPersonData?
) : Parcelable