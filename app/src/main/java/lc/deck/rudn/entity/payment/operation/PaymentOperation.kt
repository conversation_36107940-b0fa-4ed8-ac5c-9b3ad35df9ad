package lc.deck.rudn.entity.payment.operation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.payment.contract.Currency


@Parcelize
data class PaymentOperation(
    @SerializedName("accountPeriods")
    val accountPeriods: List<AccountPeriod>?,
    @SerializedName("currency")
    val currency: Currency?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("pay_date")
    val payDate: String?,
    @SerializedName("pay_status")
    val payStatus: PayStatus?,
    @SerializedName("sum")
    val sum: Number?
) : Parcelable