package lc.deck.rudn.entity.medicine.application.send


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DeclinedFilesSend(
    @SerialName("documents")
    val documents: List<Document>,
    @SerialName("service_guid")
    val serviceGuid: String
)

@Serializable
data class Document(
    @SerialName("file")
    val `file`: List<File>,
    @SerialName("guid")
    val guid: String
)

@Serializable
data class File(
    @SerialName("base64")
    val base64: String,
    @SerialName("format")
    val format: String,
    @SerialName("name")
    val name: String
)