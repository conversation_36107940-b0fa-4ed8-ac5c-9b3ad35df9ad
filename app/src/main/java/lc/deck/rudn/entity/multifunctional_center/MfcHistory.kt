package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcHistory(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("ticket_number")
    val ticketNumber: String?,
    @SerializedName("reason_call")
    val reasonCall: String?,
    @SerializedName("applicantCategory")
    val applicantCategory: List<String?>?,
    @SerializedName("service")
    val service: MfcService,
    @SerializedName("datetime")
    val dateTime: String?,
    @SerializedName("date")
    val date: String?,
    @SerializedName("referenceOrder")
    val referenceOrder: MfcReferenceOrder?,
    @SerializedName("serviceOrder")
    val serviceOrder: ServiceOrder?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("status")
    val status: MfcStatus?,
    @SerializedName("statusFlow")
    val statusFlow: List<MfcStatus>,
    @SerializedName("ready_time")
    val readyTime: String?,
    @SerializedName("price")
    val price: String,
    @SerializedName("is_need_to_pay")
    val isNeedToPay: Boolean,
    @SerializedName("serviceType")
    val serviceType: MfcServiceType?,
    @SerializedName("serviceOrderFiles")
    val files: List<FinishedDocuments>,
    @SerializedName("applicationOrder")
    val applicationOrder: ApplicationOrder?,
    @SerializedName("contract")
    val contract: Contract?,
    @SerializedName("payment")
    val payment : List<PaymentOrderCopy?>?
) : Parcelable