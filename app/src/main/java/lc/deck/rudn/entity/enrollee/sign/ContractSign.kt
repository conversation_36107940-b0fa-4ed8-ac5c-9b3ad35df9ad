package lc.deck.rudn.entity.enrollee.sign


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractSign(
    @SerializedName("phone")
    val phone: String?,
    @SerializedName("sendCodeDelay")
    val sendCodeDelay: Long?,
    @SerializedName("status")
    val status: String?
) : Parcelable

@Parcelize
enum class ContractSignType : Parcelable {
    DORM_TYPE,
    ENROLLEE_TYPE,
    MEDICAL_TYPE
}