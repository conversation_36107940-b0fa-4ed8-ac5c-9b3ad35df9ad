package lc.deck.rudn.entity.statements

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 0009 Оплачиваемый отпуск вне графика
 */
@Parcelize
data class VacationStatement(
    @SerializedName("applicationDate")
    override var applicationDate: String = "",
    @SerializedName("jobTitle")
    override var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    override var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    override var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    override var rate: Double = 0.0,
    @SerializedName("attachments")
    override var attachments: MutableList<Attachment> = mutableListOf(),
    @SerializedName("reasoning")
    val reasoning: String = "",
    @SerializedName("compensationDays")
    val compensationDays: Int? = null,
    @SerializedName("periods")
    val periods: MutableList<Period> = mutableListOf(),
) : Statement() {

    fun toRequest() = VacationStatementRequest(
        applicationDate, jobTitle, subdivision, headquarter, rate, attachments, reasoning, compensationDays, periods
    )
}

@Parcelize
data class VacationStatementRequest(
    @SerializedName("applicationDate")
    val applicationDate: String = "",
    @SerializedName("jobTitle")
    val jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    val subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    val headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    val rate: Double = 0.0,
    @SerializedName("attachments")
    val attachments: List<Attachment> = emptyList(),
    @SerializedName("reasoning")
    val reasoning: String = "",
    @SerializedName("days")
    val compensationDays: Int? = null,
    @SerializedName("periods")
    val periods: MutableList<Period> = mutableListOf(),
): Parcelable
