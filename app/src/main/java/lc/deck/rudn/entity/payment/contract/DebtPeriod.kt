package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DebtPeriod(

	@SerializedName("date_start")
	val dateStart: String? = null,

	@SerializedName("fine")
	val fine: Fine? = null,

	@SerializedName("period_data")
	val periodData: String? = null,

	@SerializedName("guid")
	val guid: String? = null,

	@SerializedName("sum")
	val sum: Int? = null,

	@SerializedName("currency")
	val currency: String? = null,

	@SerializedName("date_end")
	val dateEnd: String? = null,

	@SerializedName("nomenclature_guid")
	val nomenclatureGuid: String? = null
) : Parcelable
