package lc.deck.rudn.entity.statements

import android.os.Parcelable
import com.google.gson.annotations.SerializedName

sealed class Statement(
    @SerializedName("applicationDate")
    open var applicationDate: String = "",
    @SerializedName("jobTitle")
    open var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    open var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    open var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    open var rate: Double = 0.0,
    @SerializedName("attachments")
    open var attachments: MutableList<Attachment> = mutableListOf(),
): Parcelable
