package lc.deck.rudn.entity.portfolio

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ProfileResponse(
    @SerializedName("academicPerformanceRating")
    val academicPerformanceRating: Double?,
    @SerializedName("extracurricularActivitiesRating")
    val extracurricularActivitiesRating: Int?,
    @SerializedName("helpInfo")
    val helpInfo: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("overallRating")
    val overallRating: Double?,
    @SerializedName("scientificActivityRating")
    val scientificActivityRating: Int?
) : Parcelable
