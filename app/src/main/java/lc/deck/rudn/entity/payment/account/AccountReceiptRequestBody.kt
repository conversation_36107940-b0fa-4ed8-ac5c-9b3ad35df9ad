package lc.deck.rudn.entity.payment.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.payment.contract.PeriodSend

/**
 * Created by Seregaryz on 06.04.2022.
 */
@Parcelize
data class AccountReceiptRequestBody(
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("contract_periods")
    val contractPeriods: List<PeriodSend>
): Parcelable
