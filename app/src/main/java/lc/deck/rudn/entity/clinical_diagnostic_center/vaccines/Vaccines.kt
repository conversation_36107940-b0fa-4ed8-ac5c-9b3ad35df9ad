package lc.deck.rudn.entity.clinical_diagnostic_center.vaccines


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Vaccines(
    @SerialName("comment")
    val comment: String?,
    @SerialName("date")
    val date: String?,
    @SerialName("id")
    val id: String?,
    @SerialName("name")
    val name: String?,
    @SerialName("type")
    val type: String? = null
)