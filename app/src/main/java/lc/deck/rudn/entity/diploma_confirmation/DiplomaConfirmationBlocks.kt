package lc.deck.rudn.entity.diploma_confirmation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class DiplomaConfirmationBlocks(
    @SerializedName("blockInfo")
    val blockInfo: List<Subject?>?,
    @SerializedName("canUploadFiles")
    val canUploadFiles: Boolean?,
    @SerializedName("confirmationDate")
    val confirmationDate: String?,
    @SerializedName("confirmationDeadlineDate")
    val confirmationDeadlineDate: String?,
    @SerializedName("creationDate")
    val creationDate: String?,
    @SerializedName("diplomaGUID")
    val diplomaGUID: String?,
    @SerializedName("diplomaName")
    val diplomaName: String?,
    @SerializedName("files")
    var files: List<File?>?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("status")
    val status: DiplomaStatus?,
    @SerializedName("tableInfo")
    val tableInfo: TableInfo?,
    @SerializedName("type")
    val type: Int?
) : Parcelable
