package lc.deck.rudn.entity.dormitory_old.dorm_check_in

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 20.04.2022, среда
 **/
@Parcelize
data class ChosenTime(
    @SerializedName("arrival_date")
  val arrivalDate:String?,
    @SerializedName("arrival_time")
    val arrivalTime:String?,
    @SerializedName("application_number")
    val applicationNumber:String? = null,
) : Parcelable