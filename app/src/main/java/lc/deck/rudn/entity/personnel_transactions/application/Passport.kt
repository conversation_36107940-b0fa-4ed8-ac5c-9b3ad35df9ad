package lc.deck.rudn.entity.personnel_transactions.application


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Passport(
    @SerializedName("division_code")
    var divisionCode: String?= "",
    @SerializedName("format")
    var format: String?= "",
    @SerializedName("issue_date")
    var issueDate: String?= "",
    @SerializedName("name")
    var name: String?= "",
    @SerializedName("scan")
    var scan: String?=null,
    @SerializedName("series_number")
    var seriesNumber: String?= "",
    @SerializedName("size")
    var size: String?= ""
) : Parcelable{
    companion object{
        const val DIVISION_CODE_TYPE = "DIVISION_CODE_TYPE"
        const val SERIES_NUMBER_TYPE = "SERIES_NUMBER_TYPE"
    }
}