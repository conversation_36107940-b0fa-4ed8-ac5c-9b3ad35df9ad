package lc.deck.rudn.entity.enrollee.support_description

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class SupportDescription(
    @SerializedName("personal_error_text")
    var personalErrorText: PersonalErrorText?,
    @SerializedName("phone_error_text")
    var phoneErrorText: PhoneErrorText?
) : Parcelable
