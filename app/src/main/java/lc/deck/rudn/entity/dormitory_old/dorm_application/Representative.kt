package lc.deck.rudn.entity.dormitory_old.dorm_application


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Representative(
    @SerializedName("address")
    val address: String?,
    @SerializedName("fio")
    val fio: String?,
    @SerializedName("position")
    val position: String?,
    @SerializedName("work_place")
    val workPlace: String?
): Parcelable