package lc.deck.rudn.entity.fok.sport_grounds


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Playground(
    @SerialName("address")
    val address: String?,
    @SerialName("area")
    val area: Int?,
    @SerialName("capacity")
    val capacity: Int?,
    @SerialName("description")
    val description: String?,
    @SerialName("id")
    val id: String?,
    @SerialName("latitude")
    val latitude: Double?,
    @SerialName("longitude")
    val longitude: Double?,
    @SerialName("name")
    val name: String?,
    @SerialName("photoUrl")
    val photoUrl: String?,
    @SerialName("pictures")
    val pictures: List<String>?,
    @SerialName("sportTypes")
    val sportTypes: List<SportType>?,
    @SerialName("subPlaygrounds")
    val subPlaygrounds: List<SubPlayground>?
)