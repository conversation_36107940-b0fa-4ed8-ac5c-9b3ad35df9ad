package lc.deck.rudn.entity.diploma_confirmation

import kotlinx.android.parcel.Parcelize
import android.os.Parcelable
import com.google.gson.annotations.SerializedName

@Parcelize
data class File(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("name")
    val name: String?,
    @SerializedName("data")
    val data: String?,
    @SerializedName("size")
    val size: Long?,
) : Parcelable