package lc.deck.rudn.entity.statements

import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 0005 Отпуск без з/п
 */
@Parcelize
data class LeaveStatement(
    @SerializedName("applicationDate")
    override var applicationDate: String = "",
    @SerializedName("jobTitle")
    override var jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    override var subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    override var headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    override var rate: Double = 0.0,
    @SerializedName("attachments")
    override var attachments: MutableList<Attachment> = mutableListOf(),
    @SerializedName("reason")
    val reason: StatementReason = StatementReason(),
    @SerializedName("applicationDateEnd")
    var applicationDateEnd: String = "",
) : Statement() {

    fun toRequest() = LeaveStatementRequest(
        applicationDate, jobTitle, subdivision, headquarter, rate, attachments, reason, applicationDateEnd
    )
}

data class LeaveStatementRequest(
    @SerializedName("applicationDate")
    val applicationDate: String = "",
    @SerializedName("jobTitle")
    val jobTitle: JobTitle = JobTitle(),
    @SerializedName("subdivision")
    val subdivision: Subdivision = Subdivision(),
    @SerializedName("headquarter")
    val headquarter: Headquarter = Headquarter(),
    @SerializedName("rate")
    val rate: Double = 0.0,
    @SerializedName("attachments")
    val attachments: List<Attachment> = emptyList(),
    @SerializedName("reason")
    val reason: StatementReason = StatementReason(),
    @SerializedName("applicationDateEnd")
    var applicationDateEnd: String = "",
)

