package lc.deck.rudn.entity.employee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Employee(
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("employeePositions")
    val employeePositions: List<EmployeePosition>?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("updated_at")
    val updatedAt: String?,
    @SerializedName("user")
    val user: User?,
    @SerializedName("user_id")
    val userId: Int?,
    @SerializedName("degree")
    val degree: DegreeX?,
    @SerializedName("academicTitle")
    val academicTitle: AcademicTitleX?,
) : Parcelable {
    fun fullName(): String? =
        listOfNotNull(
            user?.person?.surnameRus,
            user?.person?.nameRus,
            user?.person?.patronymicRus
        ).joinToString(" ")

    fun shortName(): String? =
        StringBuilder().apply {
            if (!user?.person?.surnameRus.isNullOrEmpty()) append(user?.person?.surnameRus)
            if (!user?.person?.nameRus.isNullOrEmpty()) {
                if (isNotEmpty()) append(" ")
                append(user?.person?.nameRus?.substring(0, 1))
                append(".")
            }
            if (!user?.person?.patronymicRus.isNullOrEmpty()) {
                if (isNotEmpty()) append(" ")
                append(user?.person?.patronymicRus?.substring(0, 1))
                append(".")
            }
        }.toString()

    fun email(): String? = user?.person?.personEmail?.email

    fun degree(): String? = degree?.name

    fun academicTitle(): String? = academicTitle?.name

    fun positions(): String? = StringBuilder().apply {
        employeePositions?.forEach { employeePosition ->
            if (isNotEmpty()) append("\n")
            employeePosition.position?.name?.let { append("$it, ") }
            employeePosition.department?.name?.let { append("$it, ") }
            employeePosition.department?.faculty?.name?.let { append(it) }
        }
    }.toString()
}