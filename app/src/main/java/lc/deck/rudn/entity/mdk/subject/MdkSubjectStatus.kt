package lc.deck.rudn.entity.mdk.subject

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MdkSubjectStatus(
    @SerializedName("id")
    val id : Int?,
    @SerializedName("name")
    val name : String?
) : Parcelable {
    companion object {
        const val STATUS_NEW = 1
        const val STATUS_CANCELED_1 = 2
        const val STATUS_CANCELED_2 = 3
        const val STATUS_CONFIRMED = 4
    }
}