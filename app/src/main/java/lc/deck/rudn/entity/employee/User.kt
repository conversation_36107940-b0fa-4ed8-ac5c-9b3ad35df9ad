package lc.deck.rudn.entity.employee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.Person

@Parcelize
data class User(
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("email")
    val email: String?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("isCurrent")
    val isCurrent: Boolean?,
    @SerializedName("isDefault")
    val isDefault: Boolean?,
    @SerializedName("nld")
    val nld: String?,
    @SerializedName("object_id")
    val objectId: Int?,
    @SerializedName("person")
    val person: Person?,
    @SerializedName("person_id")
    val personId: Int?,
    @SerializedName("role")
    val role: Role?,
    @SerializedName("role_id")
    val roleId: Int?,
    @SerializedName("table_number")
    val tableNumber: String?,
    @SerializedName("vacation_leftover")
    var vacationLeftover: String? = ""
) : Parcelable