package lc.deck.rudn.entity.payment.operation


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PayStatus(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?
) : Parcelable{
    companion object {
        const val PAID_1 = 1
        const val IN_PROCESS_2 = 2
    }
}