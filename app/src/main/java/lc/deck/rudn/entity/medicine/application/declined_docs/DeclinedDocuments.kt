package lc.deck.rudn.entity.medicine.application.declined_docs


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.medicine.attachement.LoadedFiles

@Serializable
data class DeclinedDocuments(
    @SerialName("comment")
    val comment: String? = null,
    @SerialName("file")
    val `file`: List<File>,
    @SerialName("formats")
    val formats: String,
    @SerialName("guid")
    val guid: String,
    @SerialName("name")
    val name: String,
    @SerialName("number_of_files")
    val numberOfFiles: Int,

    @SerialName("loaded_files")
    val loadedFiles: MutableList<LoadedFiles?>? = mutableListOf(),
    @SerialName("is_loaded")
    var loadedCount: Int? = 0,
)