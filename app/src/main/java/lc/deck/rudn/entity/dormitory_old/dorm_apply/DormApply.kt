package lc.deck.rudn.entity.dormitory_old.dorm_apply


import com.google.gson.annotations.SerializedName

data class DormApply(
    @SerializedName("fact_address")
    var factAddress: String?,
    @SerializedName("family_status_guid")
    var familyStatusGuid: String?,
    @SerializedName("organization")
    var organization: Organization?,
    @SerializedName("representatives")
    var representatives: List<Representative>?
)