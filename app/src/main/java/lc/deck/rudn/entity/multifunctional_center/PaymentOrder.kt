package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentOrder(
    @SerializedName("payment_number")
    val paymentNumber : String?,
    @SerializedName("payment_date")
    val paymentDate : String?,
    @SerializedName("payment_amount")
    val paymentAmount : String?,
    @SerializedName("payment_currency")
    val paymentCurrency : String?,
    @SerializedName("account_number")
    val accountNumber : String?
) : Parcelable
