package lc.deck.rudn.entity.dormitory.application_info

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import lc.deck.rudn.entity.dormitory.dorm_applied.File

@Serializable
data class ApplicationInfo(
    @SerialName("step_name")
    val stepName: String?,
    @SerialName("place")
    val place: String?,
    @SerialName("status")
    val status: String? = null,
    @SerialName("date_create")
    val dateCreate: String?,
    @SerialName("file")
    val file: File? = null,
    @SerialName("type_dorm_application")
    val typeDormApplication: TypeDormApplication?
)
