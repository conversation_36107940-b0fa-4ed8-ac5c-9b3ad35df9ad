package lc.deck.rudn.entity.cfo_cabinet.form_report

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.cfo_cabinet.available_reports.Department
import java.util.Date

@Parcelize
data class FormReport(
    @SerializedName("person_id")
    val personId: Int?,
    @SerializedName("report_id")
    val reportId: String?,
    @SerializedName("date_from")
    val dateFrom: String?,
    @SerializedName("date_to")
    val dateTo: String?,
    @SerializedName("departments")
    val departments: List<String>?
) : Parcelable
