package lc.deck.rudn.entity.social

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class SocialNetwork(
    @SerializedName("socialMediaLangs")
    val socialMediaLang: List<SocialMediaLang>,
    @SerializedName("url")
    val url: String,
    @SerializedName("file")
    val file: File,
    @SerializedName("name")
    val name: String?
): Parcelable