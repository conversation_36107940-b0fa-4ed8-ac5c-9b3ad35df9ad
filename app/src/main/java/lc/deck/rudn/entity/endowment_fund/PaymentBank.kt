package lc.deck.rudn.entity.endowment_fund

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentBank(
    @SerializedName("id")
    val id : Int?,
    @SerializedName("title")
    val title : String?,
    @SerializedName("link")
    val link : String?,
    @SerializedName("icon")
    val icon : String?
) : Parcelable
