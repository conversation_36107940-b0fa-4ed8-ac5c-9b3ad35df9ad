package lc.deck.rudn.entity.contact

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.File
import lc.deck.rudn.entity.contact.people.ContactPeople

@Parcelize
data class ContactGroup(

    @SerializedName("id")
    val id: Int? = null,

	@SerializedName("faculty_id")
	val facultyId: Int? = null,

	@SerializedName("staffChilds")
	val staffChilds: List<ContactGroup?>? = null,

	@SerializedName("file_id")
	val fileId: Int? = null,

	@SerializedName("name")
	val name: String? = null,

	@SerializedName("description")
	val description: String? = null,

    @SerializedName("initials")
    val initials: String? = null,

    @SerializedName("contactPeople")
    val contactsPeople: List<ContactPeople?>?,

    @SerializedName("file")
    val file: File?
): Parcelable
