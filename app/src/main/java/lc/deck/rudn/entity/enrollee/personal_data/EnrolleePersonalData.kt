package lc.deck.rudn.entity.enrollee.personal_data

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.enrollee.enrollee.Citizenship
import lc.deck.rudn.entity.enrollee.enrollee.ContactInfo
import lc.deck.rudn.entity.enrollee.enrollee.Gender

@Parcelize
data class EnrolleePersonalData(
    @SerializedName("birth_date")
    var birthDate: String?,
    @SerializedName("citizenship")
    var citizenship: Citizenship?,
    @SerializedName("contactInfo")
    var contactInfo: ContactInfo?,
    @SerializedName("gender")
    var gender: Gender?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("patronymic")
    var patronymic: String?,
    @SerializedName("address")
    var address: String?,
    @SerializedName("surname")
    var surname: String?
) : Parcelable
