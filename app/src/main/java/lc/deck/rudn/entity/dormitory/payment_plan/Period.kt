package lc.deck.rudn.entity.dormitory.payment_plan


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Period(
    @SerialName("currency")
    val currency: String?,
    @SerialName("end_date")
    val endDate: String?,
    @SerialName("guid")
    val guid: String?,
    @SerialName("payment_date")
    val paymentDate: String?,
    @SerialName("sum")
    val sum: String?,
)