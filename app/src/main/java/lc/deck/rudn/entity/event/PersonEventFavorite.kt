package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PersonEventFavorite(
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("event_id")
    val eventId: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("person_id")
    val personId: Int?
): Parcelable