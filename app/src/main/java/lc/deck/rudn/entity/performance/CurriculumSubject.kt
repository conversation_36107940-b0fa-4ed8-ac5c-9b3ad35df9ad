package lc.deck.rudn.entity.performance


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CurriculumSubject(
    @SerializedName("course")
    val course: Course?,
    @SerializedName("course_id")
    val courseId: Int?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("curriculum_id")
    val curriculumId: Int?,
    @SerializedName("finalControlForm")
    val finalControlForm: FinalControlForm?,
    @SerializedName("final_control_form_id")
    val finalControlFormId: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("semestr")
    val semester: Semester?,
    @SerializedName("semestr_id")
    val semesterId: Int?,
    @SerializedName("study_language_id")
    val studyLanguageId: Int?,
    @SerializedName("subject")
    val subject: Subject?,
    @SerializedName("subject_id")
    val subjectId: Int?,
    @SerializedName("updated_at")
    val updatedAt: String?
): Parcelable