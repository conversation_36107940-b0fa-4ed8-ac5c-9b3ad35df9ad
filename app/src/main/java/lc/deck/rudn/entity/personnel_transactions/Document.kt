package lc.deck.rudn.entity.personnel_transactions


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Document(
    @SerializedName("description")
    val description: String?,
    @SerializedName("file")
    val `file`: List<File>?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("is_allowed_to_sign")
    val isAllowedToSign: Boolean?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("publication_date")
    val publicationDate: String?,
    @SerializedName("sign_date")
    val signDate: String?,
    @SerializedName("sign_till_date")
    val signTillDate: String?,
    @SerializedName("state")
    val state: StateX?
) : Parcelable