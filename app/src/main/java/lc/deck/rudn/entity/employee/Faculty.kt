package lc.deck.rudn.entity.employee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Faculty(
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("filial_id")
    val filialId: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("object_id")
    val objectId: Int?,
    @SerializedName("subdivision_id")
    val subdivisionId: Int?,
    @SerializedName("updated_at")
    val updatedAt: String?
) : Parcelable