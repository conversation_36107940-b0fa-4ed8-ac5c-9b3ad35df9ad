package lc.deck.rudn.entity.multifunctional_center.requestCreation


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Requisites(
    @SerializedName("bank_name")
    val bankName: String?,
    @SerializedName("beneficiary_account")
    val beneficiaryAccount: String?,
    @SerializedName("bic_bank")
    val bicBank: String?,
    @SerializedName("correspondent_bank_account")
    val correspondentBankAccount: String?,
    @SerializedName("name_of_recipient")
    val nameOfRecipient: String?,
    @SerializedName("ppc_bank")
    val ppcBank: String?,
    @SerializedName("tax_number_bank")
    val taxNumberBank: String?
) : Parcelable