package lc.deck.rudn.entity.group_selection

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AcademGroup(

	@SerializedName("faculty_id")
	val facultyId: Int? = null,

	@SerializedName("updated_at")
	val updatedAt: String? = null,

	@SerializedName("name")
	val name: String? = null,

	@SerializedName("created_at")
	val createdAt: String? = null,

	@SerializedName("id")
	val id: Int? = null,

	@SerializedName("object_id")
	val objectId: Int? = null
) : Parcelable
