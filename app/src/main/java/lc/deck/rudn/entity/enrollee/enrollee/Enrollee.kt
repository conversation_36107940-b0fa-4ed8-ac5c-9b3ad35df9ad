package lc.deck.rudn.entity.enrollee.enrollee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Enrollee(
    @SerializedName("documents")
    var documents: Documents?,
    @SerializedName("guid")
    var guid: String?,
    @SerializedName("legal_representative")
    var legalRepresentative: LegalRepresentative?,
    @SerializedName("personal_data")
    var personalData: PersonalData?
) : Parcelable