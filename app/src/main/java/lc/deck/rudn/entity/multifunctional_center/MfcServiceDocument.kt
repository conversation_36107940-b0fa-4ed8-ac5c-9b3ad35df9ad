package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import kotlinx.android.parcel.RawValue
import okhttp3.MediaType
import java.io.File

@Parcelize
data class MfcServiceDocument(
    @SerializedName("id")
    val id: Int,
    @SerializedName("service_id")
    val categoryId: Int,
    @SerializedName("document")
    val document: MfcDocument,
    @SerializedName("is_attachment")
    var hasAttachments: Boolean,
    @SerializedName("need_download")
    val needDownload: Boolean?,
    @SerializedName("optional")
    var optional: Boolean?,
    @SerializedName("max_count")
    val maxCount: Int? = 1,
    @SerializedName("loaded_files")
    val loadedFiles: MutableList<LoadedFiles?>? = mutableListOf(),
    @SerializedName("is_loaded")
    var loadedCount: Int? = 0,
    ) : Parcelable

@Parcelize
data class LoadedFiles(
    @SerializedName("file_name")
    val fileId: Int?,
    @SerializedName("file_title")
    val fileTitle: String? = "",
    @SerializedName("file_title_to_send")
    var fileTitleToSend: String? = "",
    @SerializedName("file_description")
    val fileDescription: String? = "",
    @SerializedName("file_path")
    val filePath: String? = "",
    @SerializedName("file")
    val file: File? = null,
    @SerializedName("type")
    val type: @RawValue MediaType? = null,
) : Parcelable