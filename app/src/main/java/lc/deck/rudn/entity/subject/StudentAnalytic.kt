package lc.deck.rudn.entity.subject

import com.google.gson.annotations.SerializedName
import lc.deck.rudn.entity.undergraduate_academ_group.UndergraduateAcademGroup

data class StudentAnalytic(
    @SerializedName("undergraduateAcademGroup")
    val undergraduateAcademGroup: UndergraduateAcademGroup?,
    @SerializedName("workProgram")
    val subject: Subject,
    @SerializedName("totalCount")
    val totalCount: Int?,
    @SerializedName("countOfEvents")
    val countOfEvents: Int?,
    @SerializedName("countOfVisits")
    val countOfVisits: Int?,
    @SerializedName("countOfPasses")
    val countOfPasses: Int?,
    @SerializedName("percentageOfVisits")
    val percentOfVisits: Int?
)
