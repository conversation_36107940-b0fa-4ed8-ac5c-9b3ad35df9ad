package lc.deck.rudn.entity.contact

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.contact.people.ContactPeople

@Parcelize
data class ContactsData(
    @SerializedName("own")
    val ownContacts: List<ContactGroup>,

    @SerializedName("contacts")
    val contacts: List<ContactPeople>,

    @SerializedName("other")
    val others: List<ContactGroup>
): Parcelable