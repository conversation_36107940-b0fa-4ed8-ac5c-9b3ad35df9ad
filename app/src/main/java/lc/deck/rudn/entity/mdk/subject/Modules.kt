package lc.deck.rudn.entity.mdk.subject


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Modules(
    @SerializedName("end")
    val end: String?,
    @SerializedName("left")
    val left: Int?,
    @SerializedName("module")
    val module: Int?,
    @SerializedName("semester")
    val semester: Int?,
    @SerializedName("total")
    val total: Int?
) : Parcelable