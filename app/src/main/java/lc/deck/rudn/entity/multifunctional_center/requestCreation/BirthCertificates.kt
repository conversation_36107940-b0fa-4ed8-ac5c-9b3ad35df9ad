package lc.deck.rudn.entity.multifunctional_center.requestCreation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import kotlinx.android.parcel.RawValue
import kotlinx.serialization.Serializable
import okhttp3.RequestBody

@Parcelize
data class BirthCertificates(
    @SerializedName("birthdate")
    val birthdate: HashMap<String, @RawValue RequestBody>?,
    @SerializedName("date")
    val date: HashMap<String, @RawValue RequestBody>?,
    @SerializedName("fio")
    val fio: HashMap<String, @RawValue RequestBody>?,
    @SerializedName("number")
    val number: HashMap<String, @RawValue RequestBody>?,
    @SerializedName("series")
    val series: HashMap<String, @RawValue RequestBody>?,
) : Parcelable