package lc.deck.rudn.entity.enrollee.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CodeVerification(
    @SerializedName("person_id")
    val personId: Int? = null,
    @SerializedName("code")
    val code : String,
    @SerializedName("file_guid")
    val fileGuid : String? = null
) : Parcelable
