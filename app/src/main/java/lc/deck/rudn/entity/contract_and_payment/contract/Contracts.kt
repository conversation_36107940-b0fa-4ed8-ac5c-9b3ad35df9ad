package lc.deck.rudn.entity.contract_and_payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.contract_and_payment.unpaid_bill.UnpaidBill

@Parcelize
data class Contracts(
    @SerializedName("date_from")
    val dateFrom: String,
    @SerializedName("date_to")
    val dateTo: String,
    @SerializedName("contracts")
    val contracts: List<Contract?>?,
) : Parcelable