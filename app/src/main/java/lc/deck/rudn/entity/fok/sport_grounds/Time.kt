package lc.deck.rudn.entity.fok.sport_grounds


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Time(
    @SerialName("currency")
    val currency: String?,
    @SerialName("duration")
    val duration: Int?,
    @SerialName("price")
    val price: Int?,
    @SerialName("status")
    val status: Status?,
    @SerialName("timeStart")
    val timeStart: String?
)