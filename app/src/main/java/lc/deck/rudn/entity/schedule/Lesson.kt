package lc.deck.rudn.entity.schedule


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Lesson(
    @SerializedName("academ_group")
    val academGroup: List<AcademGroup?>?,
    @SerializedName("employee")
    val employee: Employee?,
    @SerializedName("event")
    val event: Event?,
    @SerializedName("event_type")
    val eventType: EventType?,
    @SerializedName("parent_workshop_type")
    val parentWorkshopType: ParentWorkshopType?,
    @SerializedName("state")
    val state: Int?,
    @SerializedName("study_group")
    val studyGroup: List<StudyGroup?>?,
    @SerializedName("subject")
    val subject: Subject?,
    @SerializedName("room")
    val room: Room?
) : Parcelable {
    companion object {
        const val STATE_1 = 1
        const val STATE_2 = 2
        const val STATE_3 = 3
    }
}