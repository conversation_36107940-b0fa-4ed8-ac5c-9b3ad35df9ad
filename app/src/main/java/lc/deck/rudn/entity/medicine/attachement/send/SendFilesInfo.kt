package lc.deck.rudn.entity.medicine.attachement.send

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 22.06.2022, среда
 **/
@Serializable
data class SendFilesInfo(
    @SerialName("service_guid")
    val serviceGuid: String,
    @SerialName("documents")
    val documents: List<DocumentSend>
)

@Serializable
data class DocumentSend(
    @SerialName("guid")
    val guid: String,
    @SerialName("file")
    val file: List<FileSend>
)

@Serializable
data class FileSend(
    @SerialName("name")
    val name: String,
    @SerialName("format")
    val format: String,
    @SerialName("base64")
    val base64: String,
)

@Serializable
data class Answer(
    @SerialName("application_guid")
    val applicationGuid: String,
    @SerialName("account_number")
    val accountNumber: String,
)
