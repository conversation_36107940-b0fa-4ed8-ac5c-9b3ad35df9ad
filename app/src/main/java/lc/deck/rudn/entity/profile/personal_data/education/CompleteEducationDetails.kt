package lc.deck.rudn.entity.profile.personal_data.education

data class CompleteEducationDetails(
    val typeEducation: String?,
    val dateOfIssue: String?,
    val educationalInstitution: String?,
    val speciality: String?,
    val series: String?,
    val number: String?,
    val qualification: String?,
    val start: String?,
    val end: String?,
    val nameCourse: String?,
    val count: Int?,
    val place: String?
)
