package lc.deck.rudn.entity.payment.operation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.payment.contract.Fine
import lc.deck.rudn.entity.payment.contract.PaidDesc

@Parcelize
data class Payment(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("contract_guid")
    val contractGuid: String?,
    @SerializedName("contract_number")
    val contractNumber: String?,
    @SerializedName("issue_date")
    val issueDate: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("sum")
    val sum: Number?,
    @SerializedName("payment_status")
    val paymentStatus: PaidDesc?,
    @SerializedName("account_periods")
    val accountPeriods: List<AccountPeriod?>?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("fine")
    val fine: Fine?
) : Parcelable {
    companion object {
        const val PAID_1 = 1
        const val OVERDUE_2 = 2
        const val IN_PROCESS_3 = 3
    }
}