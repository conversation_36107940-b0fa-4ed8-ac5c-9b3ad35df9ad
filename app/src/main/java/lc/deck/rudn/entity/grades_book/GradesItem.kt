package lc.deck.rudn.entity.grades_book

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.employee.Employee

@Parcelize
data class GradesItem(
    @SerializedName("curriculumSubject")
    val curriculumSubject: CurriculumSubject? = null,
    @SerializedName("practice")
    val practice: Practice? = null,
    @SerializedName("academYear")
    val academYear: AcademYear? = null,
    @SerializedName("curriculum_subject_id")
    val curriculumSubjectId: Int? = null,
    @SerializedName("topic")
    val topic: String? = null,
    @SerializedName("finalExamination")
    val finalExamination: FinalExamination? = null,
    @SerializedName("final_control_form_id")
    val finalControlFormId: Int? = null,
    @SerializedName("finalControlForm")
    val finalControlForm: FinalControlForm? = null,
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("performanceLastResult")
    val performanceLastResult: PerformanceLastResult? = null,
    @SerializedName("undergraduate_academ_group_id")
    val undergraduateAcademGroupId: Int? = null
) : Parcelable

@Parcelize
data class FinalExamination(
    @SerializedName("topic")
    val topic: String?,
    @SerializedName("topic_eng")
    val topicEng: String?,
    @SerializedName("committeeFinalExamination")
    val committeeFinalExamination: List<CommitteeExamination?>
) : Parcelable

@Parcelize
data class CommitteeExamination(
    @SerializedName("is_chairman")
    val isChairman: Boolean?,
    @SerializedName("employee")
    val employee: Employee?
) : Parcelable

@Parcelize
data class PerformanceLastResult(
    @SerializedName("markEcts")
    val markEcts: MarkEcts? = null,
    @SerializedName("mark_id")
    val markId: Int? = null,
    @SerializedName("pass_date")
    val passDate: String? = null,
    @SerializedName("mark_ects_id")
    val markEctsId: Int? = null,
    @SerializedName("employee")
    val employee: Employee? = null,
    @SerializedName("performance_id")
    val performanceId: Int? = null,
    @SerializedName("score")
    val score: Int? = null,
    @SerializedName("employee_id")
    val employeeId: Int? = null,
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("is_debt")
    val isDebt: Boolean? = null,
    @SerializedName("mark")
    val mark: Mark? = null
) : Parcelable

@Parcelize
data class MarkEcts(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("object_id")
    val objectId: Int? = null
) : Parcelable

@Parcelize
data class CurriculumSubject(
    @SerializedName("subject")
    val subject: Subject? = null,
    @SerializedName("zet")
    val zet: Int?,
    @SerializedName("hours_total")
    val hoursTotal: String?
) : Parcelable


@Parcelize
data class Mark(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("object_id")
    val objectId: Int? = null
) : Parcelable

@Parcelize
data class Subject(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("objectId")
    val objectId: Int? = null
) : Parcelable


@Parcelize
data class Practice(
    @SerializedName("practiceType")
    val practiceType: PracticeType? = null
) : Parcelable

@Parcelize
data class PracticeType(
    @SerializedName("name")
    val name: String? = null
) : Parcelable



