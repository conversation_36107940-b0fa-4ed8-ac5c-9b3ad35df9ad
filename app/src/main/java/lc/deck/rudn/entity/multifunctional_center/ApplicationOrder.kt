package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.multifunctional_center.requestCreation.ParentData
import lc.deck.rudn.entity.multifunctional_center.requestCreation.Requisites

@Parcelize
data class ApplicationOrder(
    @SerializedName("returnReason")
    val returnReason: ReturnReason?,
    @SerializedName("exam_scores")
    val examScores: Number?,
    @SerializedName("fatherData")
    val fatherData: ParentData?,
    @SerializedName("motherData")
    val motherData: ParentData?,
    @SerializedName("requisites")
    val requisites: Requisites?,
    @SerializedName("free_input_field")
    val freeInputField: String?,
    @SerializedName("discount_reason")
    val discountReason: DiscountReason?,
    @SerializedName("payment_option")
    val paymentOption: SingleSelectionItem?,
    @SerializedName("paymentSum")
    val paymentSum: List<PaymentSum?>?,
    @SerializedName("education_programm_type")
    val educationProgrammType: String?,
    @SerializedName("education_programm_benefits")
    val educationProgrammBenefits: String?,
    @SerializedName("date_agenda")
    val dateAgenda: String?
) : Parcelable
