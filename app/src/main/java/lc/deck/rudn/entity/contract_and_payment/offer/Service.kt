package lc.deck.rudn.entity.contract_and_payment.offer


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Service(
    @SerializedName("content")
    val content: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("nomenclature_code")
    val nomenclatureCode: String?,
    @SerializedName("price")
    val price: String?
) : Parcelable