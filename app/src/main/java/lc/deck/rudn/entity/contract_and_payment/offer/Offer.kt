package lc.deck.rudn.entity.contract_and_payment.offer


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Offer(
    @SerializedName("content")
    val content: String?,
    @SerializedName("end_date")
    val endDate: String?,
    @SerializedName("offer_number")
    val offerNumber: String?,
    @SerializedName("start_date")
    val startDate: String?,
    @SerializedName("service")
    val service: List<Service?>?,
) : Parcelable