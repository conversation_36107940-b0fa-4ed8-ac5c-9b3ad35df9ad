package lc.deck.rudn.entity.enrollee.contract_account_options


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractAccountOption(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("image")
    val image: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("payment_url")
    val paymentUrl: String?
) : Parcelable