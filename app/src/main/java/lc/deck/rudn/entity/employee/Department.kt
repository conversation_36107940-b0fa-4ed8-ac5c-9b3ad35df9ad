package lc.deck.rudn.entity.employee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Department(
    @SerializedName("faculty")
    val faculty: Faculty?,
    @SerializedName("faculty_id")
    val facultyId: Int?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("object_id")
    val objectId: Int?,
    @SerializedName("updated_at")
    val updatedAt: String?
) : Parcelable