package lc.deck.rudn.entity.documents_and_instructions

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Documents(
    @SerialName("id")
    val id: Int?,
    @SerialName("name")
    val name: String?,
    @SerialName("description")
    val description: String?,
    @SerialName("urls")
    val urls: List<UrlsOrFiles?>?,
    @SerialName("files")
    val files: List<UrlsOrFiles?>?
)
