package lc.deck.rudn.entity.journal.journal


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Student(
    @SerialName("absence")
    val absence: Absence?,
    @SerialName("academGroup")
    val academGroup: AcademGroup?,
    @SerialName("attend")
    val attend: Boolean?,
    @SerialName("factHours")
    val factHours: Int?,
    @SerialName("hoursPercent")
    val hoursPercent: Int?,
    @SerialName("id")
    val id: String?,
    @SerialName("mark")
    val mark: Mark?,
    @SerialName("name")
    val name: String?,
    @SerialName("nld")
    val nld: Int?,
    @SerialName("planHours")
    val planHours: Int?,
    @SerialName("points")
    val points: Int?,
    @SerialName("scud")
    val scud: Boolean?,
    @SerialName("totalHours")
    val totalHours: Int?,
    @SerialName("totalPoints")
    val totalPoints: Int?,
    var pointInLecture: String? = null,
    var attendInLecture: Boolean = true,
    var respectfulReason: Boolean? = null
)