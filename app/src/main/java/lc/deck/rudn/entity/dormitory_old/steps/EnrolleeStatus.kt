package lc.deck.rudn.entity.dormitory_old.steps

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class EnrolleeStatus(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("text")
    val text: String?
) : Parcelable {
    companion object {

        const val ENROLLEE_NOT_ACCEPTED = 1//Не зачислен
        const val ENROLLEE_ACCEPTED_DORM_AVAILABLE = 2//Зачислен, одобрено заселение в общежитие, есть свободные места
        const val ENROLLEE_REQUIRES_PERSONAL_DATA_CHECK = 3//Доступно заключение договора найма, необходимо проверить персональные данные
        const val ENROLLEE_DORM_CONTRACT_SIGNING = 4//Доступно подписание договора
        const val ENROLLEE_CONTRACT_SIGNED_PAYMENT_WAITING = 5//Договор подписан абитуриентом, ожидание оплаты
        const val ENROLLEE_PAYMENT_DONE_MEDICINE_CONCLUSION_AWAIT = 6//Счет оплачен, доступно получение заключения от КДЦ
        const val ENROLLEE_DORM_CHECK_IN = 7//Заключение от КДЦ получено, доступен выбор даты и времени прибытия и заселения
        const val ENROLLEE_DORM_NO_AVAILABLE = 8//Зачислен, одобрено заселение в общежитие, свободных мест нет)
        const val ENROLLEE_BUDGET_SOCIAL_SUPPORT_ACCEPTED = 9//Заявление на социальную поддержку рассмотрено, бронирование общежития одобрено
        const val ENROLLEE_BUDGET_SOCIAL_SUPPORT_NOT_ACCEPTED = 10//Заявление на социальную поддержку рассмотрено, бронирование общежития не одобрено
        const val ENROLLEE_11 = 11//
        const val ENROLLEE_CONTRACT_SIGNED_BY_REPRESENTATIVE_PAYMENT_WAITING = 14//Договор подписан законным представителем, ожидание оплаты
        const val ENROLLEE_DORM_APPLICATION_AVAILABLE = 15 //Доступна подача заявления на поселение [Для контрактника]
        const val ENROLLEE_CONTRACT_STUDY_PAID_NO_AVAILABLE_DORM = 16 //Договор на обучение оплачен, свободных мест нет[Для контрактника]),
        const val ENROLLEE_DORM_APPLICATION_NO_DECISION = 17 //Заявление на поселение подано, на рассмотрении[Для контрактника])
        const val ENROLLEE_DORM_APPLICATION_HAS_DECISION_POSITIVE = 18 //Заявление на поселение рассмотрено, бронирование одобрено
        const val ENROLLEE_DORM_APPLICATION_HAS_DECISION_NEGATIVE = 19 //Заявление на поселение рассмотрено, бронирование не одобрено, доступна подача заявления на социальную поддержку
        const val ENROLLEE_DORM_CHECK_IN_DATE_TIME_CHOSEN = 20 //Дата и время прибытия и заселения выбраны
        const val ENROLLEE_DORM_ADMISSION_ORDER_AWAIT = 21 //Ожидание Приказа о зачислении
        const val NROLLEE_WAITING_CONTRACT_STUDY_SIGN_AND_PAYMENT = 22 //Ожидание заключения договора на обучение и оплаты

    }
}