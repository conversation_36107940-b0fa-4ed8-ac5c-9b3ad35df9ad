package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * Created by Seregaryz on 05.04.2022.
 */
@Parcelize
data class Fine(

    @SerializedName("sum")
    val sum: Int? = null,

    @SerializedName("currency")
    val currency: String? = null,

    @SerializedName("fine_description")
    val fineDescription: String? = null
) : Parcelable