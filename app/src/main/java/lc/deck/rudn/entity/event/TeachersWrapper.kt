package lc.deck.rudn.entity.event

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.employee.Employee

@Parcelize
data class TeachersWrapper(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("event_work_program_section_id")
    val eventWorkProgramSectionId: Int?,
    @SerializedName("employee_id")
    val employeeId: Int?,
    @SerializedName("employee")
    val employee: Employee?
): Parcelable