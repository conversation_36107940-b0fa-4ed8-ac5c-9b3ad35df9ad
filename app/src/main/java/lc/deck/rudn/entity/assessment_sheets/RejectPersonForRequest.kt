package lc.deck.rudn.entity.assessment_sheets

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RejectPersonForRequest(

	@SerializedName("statement_reject_id")
	val statementRejectId: Int? = null,

	@SerializedName("id")
	val id: Int? = null,

	@SerializedName("person_id")
	val personId: Int? = null
) : Parcelable
