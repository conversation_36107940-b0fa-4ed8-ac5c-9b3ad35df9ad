package lc.deck.rudn.entity.enrollee.contract


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractStatus(
    @SerializedName("descriprion")
    val descriprion: String?,
    @SerializedName("guid")
    val guid: String?
) : Parcelable {
    companion object {
        const val PERSONAL_INFO_CHECK = "1"
        const val CHOOSE_PAYMENT_SCHEDULE = "2"
        const val STATUS_UNDER_18 = "6"
        const val STATUS_CONTRACT_SIGNED_PAYMENT_AWAIT_4 = "4"
        const val STATUS_CONTRACT_SIGNED_BY_REPRESENTATIVE_PAYMENT_AWAIT_8 = "8"
        const val GUID_14 = "14"
        const val GUID_12 = "12"

    }
}