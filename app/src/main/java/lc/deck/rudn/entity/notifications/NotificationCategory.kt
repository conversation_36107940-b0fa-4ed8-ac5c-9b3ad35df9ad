package lc.deck.rudn.entity.notifications


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class NotificationCategory(
    @SerializedName("id")
    val id: Int?, // 5
    @SerializedName("name")
    val name: String?, // null
    @SerializedName("module")
    val module: Module?,
    @SerializedName("module_id")
    val moduleId: Int?, // 2
    @SerializedName("description")
    val description: String? // Получайте уведомления
): Parcelable {
    companion object {
        const val DOCUMENTS = 1
        const val SCHEDULE = 2
        const val PAYMENT_ARREARS = 4
        const val EDUCATION_PAYMENT = 3
        const val COMMENTS = 5
        const val EDUCATION = 6
        const val REFERENCE = 7
        const val COVID = 8
        const val INVOICE = 10
        const val SUPPORT = 11
    }
}