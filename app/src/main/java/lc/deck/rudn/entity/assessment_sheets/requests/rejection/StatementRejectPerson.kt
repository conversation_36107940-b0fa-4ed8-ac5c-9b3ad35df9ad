package lc.deck.rudn.entity.assessment_sheets.requests.rejection

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.assessment_sheets.Person

@Parcelize
data class StatementRejectPerson(

	@SerializedName("id")
	val id: Int? = null,

	@SerializedName("is_default")
	val isDefault: Boolean? = null,

	@SerializedName("tableNumber")
	val tableNumber: List<String?>? = null,

    @SerializedName("person")
    val person: Person? = null

) : Parcelable
