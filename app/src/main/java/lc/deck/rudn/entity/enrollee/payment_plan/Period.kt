package lc.deck.rudn.entity.enrollee.payment_plan


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Period(
    @SerializedName("course")
    val course: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("end_date")
    val endDate: String?,
    @SerializedName("group_by")
    val groupBy: String?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("module")
    val module: String?,
    @SerializedName("payment_date")
    val paymentDate: String?,
    @SerializedName("semester")
    val semester: String?,
    @SerializedName("start_date")
    val startDate: String?,
    @SerializedName("sum")
    val sum: String?,
    @SerializedName("year")
    val year: String?
) : Parcelable