package lc.deck.rudn.entity.dormitory_old.family_status


import com.google.gson.annotations.SerializedName

data class FamilyStatus(
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("name")
    val name: String?
) {
    companion object {
        const val STATUS_FULL_FAMILY = "1"
        const val STATUS_ONE_PARENT ="2"
        const val STATUS_GUARDIAN = "3"
        const val STATUS_ORGANIZATION ="4"
        const val STATUS_NO_PARENTS_NO_GUARDIAN = "5"
    }
}