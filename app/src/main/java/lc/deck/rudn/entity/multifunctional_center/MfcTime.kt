package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcTime(
    @SerializedName("time")
    val time: String?,
    @SerializedName("status")
    val status: Int?,
    @SerializedName("isSelected")
    var isSelected: Boolean? = false,
    ): Parcelable{
    companion object{
        const val IS_PAST = 1
        const val AVAILABLE_TIME_ID = 2
        const val NOT_AVAILABLE = 3
        const val NOT_ENOUGH_TIME = 4
    }
}