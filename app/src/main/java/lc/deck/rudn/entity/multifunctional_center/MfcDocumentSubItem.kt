package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcDocumentSubItem(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("document_id")
    val documentId: Int?,
    @SerializedName("is_attachment")
    val isAttachment: Boolean?,
    @SerializedName("optional")
    val optional: Boolean?
) : Parcelable
