package lc.deck.rudn.entity.enrollee

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * <AUTHOR> <PERSON><PERSON>
 * @created : 15.03.2022, вторник
 **/
@Parcelize
data class Applicant(
    var name: String? = "Фамилия",
    var name2: String? = "Фамилия2",
    var surname: String? = "Андрей",
    var surname2: String? = "Андрей2",
    var patronymic: String? = "Владимирович",
    var patronymic2: String? = "Владимирович2",
    var id: String? = "Паспорт гражданина РФ",
    var seriesAndNumber: String? = "9488 302459",
    var gender: String? = "Мужской",
    var issueDate: String? = "28 июня 2017",
    var departmentCode: String? = "160-049",
    var issuedBy: String? = "ТЕРРИТОРИАЛЬНЫМ ПУНКТОМ УФМС ПО РЕСПУБЛИКЕ ТАТАРСТАН В НОВО-САВИНОВСКОМ РАЙОНЕ ГОРОДА КАЗАНЬ",
    var countryOfResidence: String? = "Страна проживания",
    var registrationAddress: String? = "Республика Татарстан, Казань, ул. Маршала Чуйкова, д. 31, кв. 59",
    var the_role_of_legal_representative: String? = "Родитель",
    var phone: String? = "****** 479-49-56",
    var phone2: String? = "****** 479-49-56",
    var email: String? = "<EMAIL>",
    var email2: String? = "<EMAIL>",
    var dateBirth: String? = "13 июня 2005",
    var citizenship: String? = "РФ",
    var snils: String? = "123-143-244 54",
) : Parcelable