package lc.deck.rudn.entity.notifications

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Content(
    @SerializedName("mfc")
    val mfc: NotificationMfc? = null,
    @SerializedName("pass")
    val pass: NotificationMfc? = null,
    @SerializedName("comment")
    val comment: Comment? = null,
    @SerializedName("payment")
    val payment: Payment? = null,
    @SerializedName("support")
    val support: Support?
): Parcelable