package lc.deck.rudn.entity.diploma


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Diploma(
    @SerializedName("diploma_type")
    val diplomaType: DiplomaType?,
    @SerializedName("document_type")
    val documentType: DocumentType?,
    @SerializedName("education_form")
    val educationForm: EducationForm?,
    @SerializedName("education_profile")
    val educationProfile: EducationProfile?,
    @SerializedName("end_year")
    val endYear: String?,
    @SerializedName("file")
    val `file`: List<File>?,
    @SerializedName("guid")
    val guid: String?,
    @SerializedName("issue_date")
    val issueDate: String?,
    @SerializedName("number")
    val number: String?,
    @SerializedName("program")
    val program: Program?,
    @SerializedName("qualification")
    val qualification: Qualification?,
    @SerializedName("registration_number")
    val registrationNumber: String?,
    @SerializedName("series")
    val series: String?,
    @SerializedName("start_year")
    val startYear: String?,
    @SerializedName("topic")
    val topic: String?,
    @SerializedName("is_with_honors")
    val isWithHonors: Boolean?
) : Parcelable