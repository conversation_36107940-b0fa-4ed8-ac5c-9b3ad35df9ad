package lc.deck.rudn.entity.offer

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Offer(
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("preview")
	val preview: String? = null,
    @SerializedName("date_start")
	val dateStart: String? = null,
    @SerializedName("updated_at")
	val updatedAt: String? = null,
    @SerializedName("offerLangs")
	val offerLangs: List<OfferLangItem?>? = null,
    @SerializedName("name")
	val name: String? = null,
    @SerializedName("created_at")
	val createdAt: String? = null,
    @SerializedName("date_end")
	val dateEnd: String? = null,
    @SerializedName("website_id")
	val websiteId: Int? = null,
    @SerializedName("url")
	val url: String? = null
) : Parcelable
