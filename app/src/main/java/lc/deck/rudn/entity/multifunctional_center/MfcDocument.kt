package lc.deck.rudn.entity.multifunctional_center

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MfcDocument(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("objectUuid")
    val objectUuid: MfcObjectUuid?,
    @SerializedName("show_description")
    val showDescription: <PERSON><PERSON><PERSON>,
    @SerializedName("description")
    val description: String?,
    @SerializedName("alias")
    val alias: String?,
) : Parcelable {
    companion object {
        const val REFERENCE_TO_MILITARY_OFFICE_ALIAS = "reference_to_military_office"
        const val CERTIFICATE_OF_THE_AMOUNT_OF_THE_SCHOLARSHIP_TABLE_ALIAS = "certificate_of_the_amount_of_the_scholarship_table"
        const val CERTIFICATE_SCHOLARSHIP_2 = "certificate_of_the_amount_of_the_scholarship_no_2"
        const val APPLICATION_FOR_ACADEMIC_LEAVE_FOR_A_CHILD_UP_TO_1_5 = "application_for_academic_leave_to_care_for_a_child_up_to_1.5_years"
        const val APPLICATION_FOR_ACADEMIC_LEAVE_TO_CARE_FOR_A_CHILD_UP_TO_3_YEARS_ALIAS = "application_for_academic_leave_to_care_for_a_child_up_to_3_years"
        const val ARCHIVAL_CERTIFICATE_OF_REGISTRATION = "archival_certificate_of_registration"
        const val ARCHIVAL_CERTIFICATE_OF_EDUCATION = "archival_certificate_of_education"
        const val AEP_INTERPRETER = "aep_interpreter"
        const val PREGNANCY_CHILDBIRTH_ACADEM_LEAVE_APPLICATION = "pregnancy_childbirth_academ_leave_application"
        const val EMPLOYMENT_CONTRACT_COPY = "employment_contract_copy"
        const val WORK_RECORD_BOOK_COPY = "work_record_book_copy"
        const val PAYMENT_ORDER_COPY = "payment_order_copy"
        const val WORK_PLACE_ARCHIVAL_CERTIFICATE_ALIAS = "work_place_archival_certificate"
        const val APPLICATION_FOR_ACADEMIC_LEAVE_FOR_ARMY_ALIAS = "application_for_academic_leave_in_connection_with_conscription_into_the_army"
        const val CERTIFICATE_CHILD_CARE_BENEFITS_1_5_EMPLOYEE_ALIAS = "certificate_child_care_benefits_1.5_employee"
        const val CERTIFICATE_ONE_BENEFIT_CHILD_BIRTH_EMPLOYEE_ALIAS = "сertificate_one_benefit_child_birth_employee"
        const val STUDENT_DATA_CHANGING = "student_data_changing"
        const val PORTFOLIO_ACHIEVEMENT = "portfolio_achievement"
        const val TEMPORAL_LIVING = "temporal_living"
        const val APPLICATION_FOR_REFUND = "application_for_refund"
        const val FUNDS_TRANSFER = "funds_transfer"
        const val DRIVING_SCHOOL = "driving_school"
        const val SNILS_FORMALIZATION = "snils_formalization"
        const val MEDICAL_SERVICE = "medical_service"
        const val PRELIMINARY_AGREEMENT = "preliminary_agreement"
        const val BUDGET = "budget"
        const val ARCHIVE_REGISTRATION = "archive_registration"
        const val CERTIFICATE_STUDY_PERIOD_GRADES = "certificate_study_period_grades"
    }
}