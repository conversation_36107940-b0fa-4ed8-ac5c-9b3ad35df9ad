package lc.deck.rudn.entity.payment.contract

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ContractType(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val name: String?,
) : Parcelable {

    companion object {
        const val STUDY_CONTRACT = 1
        const val DORM_CONTRACT = 2
        const val MEDICAL_CONTRACT = 3
    }
}