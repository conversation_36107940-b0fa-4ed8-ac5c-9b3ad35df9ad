package lc.deck.rudn.entity.news


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.*

@Parcelize
data class News(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("news_id")
    val newsId: Int?,
    @SerializedName("lang_id")
    val langId: Int?,
    @SerializedName("preview")
    val preview: String?,
//    @SerializedName("fulltext")
//    val fulltext: String?,
    @SerializedName("publish_datetime")
    val publishDatetime: Date?,
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("website_id")
    val websiteId: Int?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("path")
    val path: String?,
    @SerializedName("newsLangImage")
    val newsLangImage: NewsLangImage?,
    @SerializedName("news")
    val news: NewsNews?,
    @SerializedName("hostname")
    val hostname: String?,
    @SerializedName("url")
    val url: String?
): Parcelable