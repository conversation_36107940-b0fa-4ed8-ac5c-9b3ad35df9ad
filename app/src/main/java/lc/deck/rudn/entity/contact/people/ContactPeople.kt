package lc.deck.rudn.entity.contact.people

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.File

@Parcelize
data class ContactPeople(

    @SerializedName("patronymic")
	val patronymic: String? = null,

    @SerializedName("surname")
	val surname: String? = null,

    @SerializedName("initials")
	val initials: String? = null,

    @SerializedName("employee_id")
	val employeeId: Int? = null,

    @SerializedName("file_id")
	val fileId: Int? = null,

    @SerializedName("staff_id")
	val staffId: Int? = null,

    @SerializedName("name")
	val name: String? = null,

    @SerializedName("address_id")
	val addressId: Int? = null,

    @SerializedName("fullName")
	val fullName: String? = null,

    @SerializedName("id")
	val id: Int? = null,

    @SerializedName("position")
	val position: String? = null,

    @SerializedName("contactPersonPhones")
    val contactPersonPhones: List<PersonPhone?>?,

    @SerializedName("contactPersonEmails")
    val contactPersonEmails: List<PersonEmail?>?,

    @SerializedName("address")
    val address: ContactAddress?,

    @SerializedName("file")
    val file: File?
): Parcelable
