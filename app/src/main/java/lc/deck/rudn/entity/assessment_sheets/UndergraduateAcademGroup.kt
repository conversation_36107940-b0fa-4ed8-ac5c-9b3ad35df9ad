package lc.deck.rudn.entity.assessment_sheets


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class UndergraduateAcademGroup(
    @SerializedName("certification_result")
    var certificationResult: Int?,
    @SerializedName("is_absent")
    var isAbsent: Boolean?,
    @SerializedName("is_correction_allowed")
    val isCorrectionAllowed: Boolean?,
    @SerializedName("is_result")
    val isResult: Boolean?,
    @SerializedName("mark")
    var mark: Mark?,
  /*  @SerializedName("mark_old")
    var markOld: Mark?,*/
    @SerializedName("markEcts")
    var markEcts: MarkEcts?,
 /*   @SerializedName("markEcts_old")
    var markEctsOld: MarkEcts?,*/
    @SerializedName("semestr_result")
    var semestrResult: Int?,
    @SerializedName("statement_student_id")
    val statementStudentId: Int?,
    @SerializedName("student_state_id")
    val studentStateId: Int?,
    @SerializedName("topic")
    val topic: String?,
    @SerializedName("topic_eng")
    val topicEng: String?,
    @SerializedName("total_result")
    var totalResult: Int?,
   /* @SerializedName("total_result_old")
    var totalResultOld: Int?,*/
    @SerializedName("trial_number")
    val trialNumber: Int?,
    @SerializedName("undergraduateAcademGroup")
    val undergraduateAcademGroup: UndergraduateAcademGroupX?,
    @SerializedName("is_topic_needed")
    val isTopicNeeded: Boolean? = true,
    @SerializedName("is_selected")
    var isSelected: Boolean? = false,
    @SerializedName("is_mistake")
    var isMistake: Boolean? = false, //TODO
   /* @SerializedName("is_changed")
    var isChanged: Boolean? = false,//TODO,*/
  /*  @SerializedName("isAbsentOld")
    var isAbsentOld: Boolean?//TODO*/
) : Parcelable