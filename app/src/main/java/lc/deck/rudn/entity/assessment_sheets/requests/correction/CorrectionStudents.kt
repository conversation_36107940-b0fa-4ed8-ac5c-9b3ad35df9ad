package lc.deck.rudn.entity.assessment_sheets.requests.correction


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.assessment_sheets.UndergraduateAcademGroupX

@Parcelize
data class CorrectionStudents(
    @SerializedName("decision")
    val decision: Decision?,
    @SerializedName("id")
    val id: Int?,
    @SerializedName("newCorrectionStudentResult")
    val newCorrectionStudentResult: CorrectionStudentResult?,
    @SerializedName("oldCorrectionStudentResult")
    val oldCorrectionStudentResult: CorrectionStudentResult?,
    @SerializedName("reason")
    var reason: String?,
    @SerializedName("undergraduateAcademGroup")
    val undergraduateAcademGroup: UndergraduateAcademGroupX?,
    @SerializedName("statement_student_id")
    val statementStudentId: Int?
):Parcelable