package lc.deck.rudn.entity.multifunctional_center


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ReferenceRole(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("reference_id")
    val referenceId: Int?,
    @SerializedName("role")
    val role: Role?,
    @SerializedName("role_id")
    val roleId: Int?
): Parcelable