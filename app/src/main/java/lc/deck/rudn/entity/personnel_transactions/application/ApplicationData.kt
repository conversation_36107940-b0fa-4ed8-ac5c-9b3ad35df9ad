package lc.deck.rudn.entity.personnel_transactions.application


import android.os.Parcelable
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ApplicationData(
    @SerializedName("birthdate")
    val birthdate: String?,
    @SerializedName("emails")
    var emails: List<String>?,
    @SerializedName("email")
    var email: String?,
    @SerializedName("errors")
    val errors: String?,
    @SerializedName("gender")
    val gender: String?,
    @SerializedName("inn")
    var inn: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("passport")
    val passport: Passport?,
    @SerializedName("patronymic")
    var patronymic: String?,
    @SerializedName("phone")
    var phone: String?,
    @SerializedName("snils")
    var snils: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("surname")
    var surname: String?,
    @SerializedName("user")
    val user: String?
) : Parcelable {

    fun deepCopy(): ApplicationData {
        val JSON = Gson().toJson(this)
        return Gson().fromJson(JSON, ApplicationData::class.java)
    }

    companion object {
        const val PHONE_TYPE = "PHONE_TYPE"
        const val SURNAME_TYPE = "SURNAME_TYPE"
        const val NAME_TYPE = "NAME_TYPE"
        const val PATRONYMIC_TYPE = "PATRONYMIC_TYPE"
        const val EMAIL_TYPE = "EMAIL_TYPE"
        const val INN_TYPE = "INN_TYPE"
        const val SNILS_TYPE = "SNILS_TYPE"
    }
}