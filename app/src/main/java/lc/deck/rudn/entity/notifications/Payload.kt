package lc.deck.rudn.entity.notifications


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Payload(
    @SerializedName("text")
    val text: String? = null, // null
    @SerializedName("action")
    val action: Action? = null,
    @SerializedName("content")
    val content: Content?,
    @SerializedName("action_id")
    val actionId: Int? = null, // 5
    @SerializedName("person_id")
    val personId: Int? = null, // 1
    @SerializedName("from_person_id")
    val fromPersonId: Int? = null, // 2
    @SerializedName("notificationCategory")
    val notificationCategory: NotificationCategory?,
    @SerializedName("notification_category_id")
    val notificationCategoryId: Int?, // 5
) : Parcelable