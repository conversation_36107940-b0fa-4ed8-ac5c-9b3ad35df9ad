package lc.deck.rudn.entity.subject

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import lc.deck.rudn.entity.undergraduate_academ_group.AcademGroup
import lc.deck.rudn.entity.undergraduate_academ_group.UndergraduateAcademGroup

@Parcelize
data class Subject(

    @SerializedName("subject_id")
    val subjectId: Int? = null,

    @SerializedName("parent_workshop_type_id")
    val parentWorkshopTypeId: Int? = null,

    @SerializedName("subject")
    val subjectChild: SubjectChild? = null,

    @SerializedName("id")
    val id: Int? = null,

    @SerializedName("parentWorkshopType")
    val parentWorkshopType: SubjectType? = null,

    @SerializedName("academGroups")
    val groups: List<AcademGroup>?,
    @SerializedName("undergraduateAcademGroups")
    val undergraduateAcademGroup: List<UndergraduateAcademGroup>?,
    @SerializedName("curriculum_id")
    val curriculumId: Int?,

    @SerializedName("department_id")
    val departmentId: Int?,

    @SerializedName("object_id")
    val objectId: Int?,

    @SerializedName("total_hour")
    val totalHour: Int?,

    @SerializedName("created_at")
    val createdAt: String?,

    @SerializedName("updated_at")
    val updatedAt: String?,

    @SerializedName("current_semester")
    val currentSem: lc.deck.rudn.entity.subject.Semester?,

    @SerializedName("current_academ_year")
    val currentYear: AcademYear?
) : Parcelable
