package lc.deck.rudn.entity.event


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Activity(
    @SerializedName("id")
    val id: Int?, // 4
//    @SerializedName("activity_format_id")
//    val activityFormatId: Int?, // 1
//    @SerializedName("activity_type_id")
//    val activityTypeId: Int?, // 1
//    @SerializedName("updated_at")
//    val updatedAt: Any?, // null
//    @SerializedName("created_at")
//    val createdAt: Any?, // null
//    @SerializedName("activity_organizer_id")
//    val activityOrganizerId: Int?, // 1
//    @SerializedName("activity_id")
//    val activityId: Int?, // 4
    @SerializedName("name")
    val name: String?, // Тестовое: Региональный онлайн круглый стол «Приемная кампания 2020 в условиях эпидемиологических ограничений. Открой мир в одном Университете»
//    @SerializedName("website_id")
//    val websiteId: Int?, // 1
    @SerializedName("preview")
    val preview: String?, // Российский университет дружбы народов проводит региональный круглый стол для осведомления иностранных абитуриентов из стран Азии о правилах поступления на обучение в РУДН.
    @SerializedName("fulltext")
    val fulltext: String?, // Российский университет дружбы народов рассматривает вопрос обучения иностранных студентов, как одну из приоритетных задач Университета по продвижению за рубежом российского образования, русского языка и культуры. Основной целью Круглого стола является освещение и разъяснение правил приема для поступления в РУДН иностранных граждан, а также презентация инновационного проекта, реализованного на базе Университета «Цифровой подготовительный факультет РУДН».Руководствуясь указанными целями, Университет выступает инициатором проведения онлайн «Круглого стола», созданного для ответа на интересующие вопросы от абитуриентов из стран Азии об обучении и поступлении.
    @SerializedName("place")
    val place: String?, // Место тестовое
//    @SerializedName("is_deleted")
//    val isDeleted: Boolean?, // false
//    @SerializedName("path")
//    val path: String?, // regionalnyy-onlayn-kruglyy-stol-priemnaya-kampaniya-2020-v-usloviyah-epidemiologicheskih-ogranicheniy-otkroy-mir-v-odnom-universitete
    @SerializedName("url")
    val url: String?, // rudn.ru/media/events/regionalnyy-onlayn-kruglyy-stol-priemnaya-kampaniya-2020-v-usloviyah-epidemiologicheskih-ogranicheniy-otkroy-mir-v-odnom-universitete
    @SerializedName("place_real")
    val placeReal: String?, // Участие online
    @SerializedName("activityFormat")
    val activityFormat: ActivityFormat?,
//    @SerializedName("activityType")
//    val activityType: ActivityType?,
    @SerializedName("activityOrganizer")
    val activityOrganizer: ActivityOrganizer?,
    @SerializedName("activityDirections")
    val activityDirections: List<ActivityDirection>?,
    @SerializedName("activityDirectionParents")
    val activityDirectionParents: List<ActivityDirectionParent>?,
    @SerializedName("activityContactPersonRudn")
    val activityContactPersonRudn: ActivityContactPersonRudn?
): Parcelable