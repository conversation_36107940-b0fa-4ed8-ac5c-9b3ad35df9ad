package lc.deck.rudn.entity.enrollee.enrollee


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PersonalData(
    @SerializedName("birth_date")
    var birthDate: String?,
    @SerializedName("citizenship")
    var citizenship: Citizenship?,
    @SerializedName("contact_info")
    var contactInfo: ContactInfo?,
    @SerializedName("gender")
    var gender: Gender?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("patronymic")
    var patronymic: String?,
    @SerializedName("registration")
    var registration: Registration?,
    @SerializedName("surname")
    var surname: String?
) : Parcelable