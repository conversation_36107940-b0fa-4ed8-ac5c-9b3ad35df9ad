package lc.deck.rudn.data.cache

import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class EventCalendarCache @Inject constructor(){

    data class Calendar(val map: Map<String, Any>)

    private val cacheMap = ConcurrentHashMap<Map<String?, String?>, Calendar>()

    fun put(filters: Map<String?, String?>, map: Map<String, Any>) {
        cacheMap[filters] = Calendar(map)
    }

    fun get(filters: Map<String?, String?>): Map<String, Any>? {
        return cacheMap[filters]?.map
    }

    fun dropCache() {
        cacheMap.clear()
    }

}