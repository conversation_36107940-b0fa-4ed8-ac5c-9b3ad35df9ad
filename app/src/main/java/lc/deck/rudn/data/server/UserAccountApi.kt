package lc.deck.rudn.data.server

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.reactivex.Single
import lc.deck.rudn.BuildConfig
import lc.deck.rudn.entity.BaseResponse
import lc.deck.rudn.entity.user.UserAccount
import lc.deck.rudn.system.LocaleHolder
import lc.deck.rudn.ui.auth.sign_in_365.SignIn365Fragment
import okhttp3.OkHttpClient
import okhttp3.Request
import timber.log.Timber
import javax.inject.Inject

class UserAccountApi @Inject constructor(
    private val gson: Gson,
    private val localeHolder: LocaleHolder,
    okHttpClientBuilder: OkHttpClient.Builder
) {

    private val okHttpClient = okHttpClientBuilder.build()
    private val responseType = object : TypeToken<BaseResponse<Any?>>() {}.type


    fun signInOffice365(code: String, authType: Int): Single<UserAccount> =
        Single.defer<UserAccount> {

            Timber.e(code)

            var partUrl = ""
            when (authType) {
                SignIn365Fragment.ARG_RUDN_ID ->
                    partUrl = "token-rudn-id"

                SignIn365Fragment.ARG_MICROSOFT ->
                    partUrl = "token"
            }

            val request = Request.Builder()
                .url("${BuildConfig.BASE_URL}v1/auth/$partUrl?code=$code")
                .header("Accept-Language", localeHolder.getLocale())
                .build()

            try {
                val response = okHttpClient.newCall(request).execute()
                if (response.isSuccessful) {

//                    val responseType = object : TypeToken<BaseResponse<UserAccount>>() {}.type
                    val responseType = TypeToken.getParameterized(
                        BaseResponse::class.java,
                        UserAccount::class.java
                    ).type

                    val baseResponse =
                        gson.fromJson<BaseResponse<UserAccount>>(
                            response.body()?.charStream(),
                            responseType
                        )

                    Timber.d(baseResponse.data?.token ?: "")
                    return@defer Single.just(baseResponse.data)

                } else {
                    val baseResponse =
                        gson.fromJson<BaseResponse<Any>>(
                            response.body()?.charStream(),
                            responseType
                        )
                    throw RuntimeException(baseResponse.error?.message)
                }

            } catch (e: Exception) {
                return@defer Single.error(e)
            }
        }
}