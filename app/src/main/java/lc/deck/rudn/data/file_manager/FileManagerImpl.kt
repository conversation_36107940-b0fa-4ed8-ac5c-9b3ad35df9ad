package lc.deck.rudn.data.file_manager

import android.content.Context
import android.os.Environment
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileManagerImpl @Inject constructor(
    @ApplicationContext private val context: Context
): FileManager {

    @Throws(IOException::class)
    override fun createImageFile(): File {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "JPEG_${System.currentTimeMillis()}",
            ".jpg",
            storageDir
        )
    }

    override fun createVideoFile(): File {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "MP4_${System.currentTimeMillis()}",
            ".mp4",
            storageDir
        )
    }

    override fun createMfcFile(type: String): File {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "FILE_${System.currentTimeMillis()}",
            ".${type}",
            storageDir
        )
    }

    @Throws(IOException::class)
    override fun createSuggestionFile(type: String): File {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "FILE_${System.currentTimeMillis()}",
            ".${type}",
            storageDir
        )
    }
}