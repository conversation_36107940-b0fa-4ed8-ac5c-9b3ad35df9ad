package lc.deck.rudn.data.speech.adaptive_card

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import lc.deck.rudn.utils.onlyDigits
import java.lang.reflect.Type

class AdaptiveCardDeserializer : JsonDeserializer<ContentRaw> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): ContentRaw {
        val minHeightPx: Int? =
            try {
                val heightRaw = json.asJsonObject.get("minHeight").asString
                heightRaw.onlyDigits().toInt()
            } catch (e: Throwable) {
                null
            }
        return ContentRaw(
            value = json.toString(),
            minHeightPx = minHeightPx
        )
    }

}