package lc.deck.rudn.data.calendar

import lc.deck.rudn.entity.calendar.CalendarMonthWeek
import lc.deck.rudn.entity.calendar.Month
import lc.deck.rudn.entity.calendar.Week
import java.util.*
import kotlin.collections.ArrayList

class CalendarDataSource(
    private val rawStartDate: Date,
    private val rawEndDate: Date
) {
    private val calendar = GregorianCalendar.getInstance().apply {
        firstDayOfWeek = Calendar.MONDAY
        minimalDaysInFirstWeek = 1
    }
    private val startDate: Date = setupStartDate(calendar)
    private val endDate: Date = setupEndDate(calendar)
    val days: List<Date> = initDayItems(calendar, startDate, endDate)
    val weeks: List<Week> = initWeekItems(calendar, startDate, endDate)
    val calendarMonthWeekList: List<CalendarMonthWeek> =
        initCalendarItems(calendar, startDate, endDate)
    val calendarTabletMonthList: List<Month> = initTabletCalendarItems(calendar, startDate, endDate)

    private fun setupStartDate(calendar: Calendar): Date {
        calendar.apply {
            time = rawStartDate
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            set(Calendar.DAY_OF_MONTH, 1)
        }
        return calendar.time
    }

    private fun setupEndDate(calendar: Calendar): Date {
        calendar.apply {
            time = rawEndDate
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            set(Calendar.DAY_OF_MONTH, getActualMaximum(Calendar.DAY_OF_MONTH))
        }
        return calendar.time
    }

    private fun initDayItems(
        calendar: Calendar,
        startDate: Date,
        endDate: Date
    ): List<Date> {
        val items = ArrayList<Date>()
        calendar.time = startDate
        while (calendar.time < endDate) {
            items += calendar.time
            calendar.add(Calendar.DAY_OF_YEAR, 1)
        }
        return items
    }

    private fun initWeekItems(
        calendar: Calendar,
        startDate: Date,
        endDate: Date
    ): List<Week> {
        val items = ArrayList<Week>()
        calendar.time = startDate
        while (calendar.time < endDate) {
            val daysOfWeek = ArrayList<Date>()
            val currentWeek = calendar.get(Calendar.WEEK_OF_YEAR)
            while (currentWeek == calendar.get(Calendar.WEEK_OF_YEAR)) {
                daysOfWeek += calendar.time
                calendar.add(Calendar.DAY_OF_YEAR, 1)
            }
            items += Week(daysOfWeek)
        }
        return items
    }

    private fun initCalendarItems(
        calendar: Calendar,
        startDate: Date,
        endDate: Date
    ): List<CalendarMonthWeek> {
        val items = ArrayList<CalendarMonthWeek>()
        calendar.time = startDate
        var currentYear = calendar.get(Calendar.YEAR)
        var currentWeek = calendar.get(Calendar.WEEK_OF_YEAR)
        var currentMonth = calendar.get(Calendar.MONTH)
        while (calendar.time < endDate) {
            items += CalendarMonthWeek.Month(calendar.time)
            while (
                calendar.get(Calendar.YEAR) == currentYear &&
                calendar.get(Calendar.MONTH) == currentMonth
            ) {
                val daysOfWeek = ArrayList<Date>()
                while (
                    calendar.get(Calendar.WEEK_OF_YEAR) == currentWeek &&
                    calendar.get(Calendar.MONTH) == currentMonth
                ) {
                    daysOfWeek += calendar.time
                    calendar.add(Calendar.DAY_OF_YEAR, 1)
                }
                currentWeek = calendar.get(Calendar.WEEK_OF_YEAR)
                items += CalendarMonthWeek.Week(daysOfWeek)
            }
            currentMonth = calendar.get(Calendar.MONTH)
            currentYear = calendar.get(Calendar.YEAR)
        }
        return items
    }

    private fun initTabletCalendarItems (
        calendar: Calendar,
        startDate: Date,
        endDate: Date
    ): List<Month> {
        val items = ArrayList<Month>()
        calendar.time = startDate
        var currentYear = calendar.get(Calendar.YEAR)
        var currentWeek = calendar.get(Calendar.WEEK_OF_YEAR)
        var currentMonth = calendar.get(Calendar.MONTH)
        while (calendar.time < endDate) {
            val newItem = Month(calendar.time, arrayListOf())
            while (
                calendar.get(Calendar.YEAR) == currentYear &&
                calendar.get(Calendar.MONTH) == currentMonth
            ) {
                val daysOfWeek = ArrayList<Date>()
                while (
                    calendar.get(Calendar.WEEK_OF_YEAR) == currentWeek &&
                    calendar.get(Calendar.MONTH) == currentMonth
                ) {
                    daysOfWeek += calendar.time
                    calendar.add(Calendar.DAY_OF_YEAR, 1)
                }
                currentWeek = calendar.get(Calendar.WEEK_OF_YEAR)
                newItem.weeks += Week(daysOfWeek)
            }
            items += newItem
            currentMonth = calendar.get(Calendar.MONTH)
            currentYear = calendar.get(Calendar.YEAR)
        }
        return items
    }
}