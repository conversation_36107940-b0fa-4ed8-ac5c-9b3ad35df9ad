package lc.deck.rudn.data.server

import io.reactivex.Single
import lc.deck.rudn.data.cache.EventCalendarCache
import lc.deck.rudn.entity.BaseResponse

class ApiWithEventCalendarCache(
    private val eventCalendarCache: EventCalendarCache,
    private val api: Api
) : Api by api {

    override fun getEventIndicators(
        filters: Map<String?, String?>
    ): Single<BaseResponse<Map<String, Any>>> =
        Single.defer<BaseResponse<Map<String, Any>>> {
            val cachedEventCalendar = eventCalendarCache.get(filters)
            if (cachedEventCalendar == null) {
                api.getEventIndicators(filters)
                    .doOnSuccess { it.data?.let { it1 -> eventCalendarCache.put(filters, it1) } }
            } else {
                Single.just(BaseResponse(cachedEventCalendar, null))
            }
        }
}