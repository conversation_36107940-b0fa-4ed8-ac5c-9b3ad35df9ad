package lc.deck.rudn.data.server

import io.reactivex.Completable
import io.reactivex.Single
import lc.deck.rudn.entity.BaseResponse
import lc.deck.rudn.entity.enrollee.application.Application
import lc.deck.rudn.entity.enrollee.contract.Contract
import lc.deck.rudn.entity.enrollee.contract.ContractPaymentTypeGuid
import lc.deck.rudn.entity.enrollee.enrollee.Confirm
import lc.deck.rudn.entity.enrollee.enrollee.Enrollee
import lc.deck.rudn.entity.enrollee.enrollee.Gender
import lc.deck.rudn.entity.enrollee.payment_plan.PaymentPlan
import lc.deck.rudn.entity.enrollee.payment_schedule.PaymentSchedule
import lc.deck.rudn.entity.dormitory_old.dorm_application.DormApplication
import lc.deck.rudn.entity.dormitory_old.dorm_apply.DormApply
import lc.deck.rudn.entity.dormitory_old.dorm_check_in.ChosenTime
import lc.deck.rudn.entity.dormitory_old.dorm_check_in.DormCheckIn
import lc.deck.rudn.entity.dormitory_old.family_status.FamilyStatus
import lc.deck.rudn.entity.dormitory_old.link.DormLink
import lc.deck.rudn.entity.dormitory_old.steps.DormSteps
import lc.deck.rudn.entity.medicine.payment.Bank
import retrofit2.http.*

/**
 * <AUTHOR> Alina
 * @created : 06.04.2022, среда
 **/
interface EnrolleeTestApi {

    @GET("application")
    fun getApplicationsList(): Single<BaseResponse<List<Application?>?>>

    @GET("personal-data")
    fun getEnrollee(): Single<BaseResponse<Enrollee?>>

    @GET("gender-list")
    fun getGenderList(): Single<BaseResponse<List<Gender?>?>>

    @POST("personal-data-confirmation")
    fun sendPersonalDataConfirm(@Body is_correct: Confirm): Completable
}

interface ContractTestApi {

    @GET("contract")
    fun getContract(@QueryMap filters: Map<String?, String?> = mapOf()): Single<BaseResponse<Contract?>>

    @GET("contract/account/options")
    fun getBanks(@Query("account_number") accountNumber: String?): Single<BaseResponse<List<Bank?>?>>

    @GET("payment-type")
    fun getPaymentType(@Query("contract_guid") contractGuid: String?): Single<BaseResponse<PaymentSchedule?>>

    @POST("payment-type/select")
    fun getFormedContract(@Body body: ContractPaymentTypeGuid): Single<BaseResponse<Contract?>>

    @GET("payment-plan")
    fun getPaymentPlan(
        @Query("contract_guid") contractGuid: String?,
        @Query("payment_type_guid") paymentTypeGuid: String?
    ): Single<BaseResponse<PaymentPlan?>> {
        TODO("Not yet implemented")
    }

}

interface DormTestApi {
    @GET("dorm/steps")
    fun getDormSteps(@Query("status") status: Int? = null): Single<BaseResponse<DormSteps?>>

    @GET("dorm/link")
    fun getDormLink(): Single<BaseResponse<DormLink?>>

    @GET("dorm/time-options")
    fun getDormTimeOptions(): Single<BaseResponse<DormCheckIn?>>

    @GET("dorm/arrival")
    fun getDormArrival(): Single<BaseResponse<DormCheckIn?>>

    @GET("dorm/family-status")
    fun getFamilyStatuses(): Single<BaseResponse<List<FamilyStatus?>?>>

    @POST("dorm/apply")
    fun sendDormApplication(@Body dormApply: DormApply?): Single<BaseResponse<DormApplication?>>

    @POST("dorm/arrival-create")
    fun sendArrivalTime(@Body body: ChosenTime): Single<BaseResponse<DormCheckIn?>>

    @PATCH("dorm/arrival-update")
    fun sendArrivalTimeUpdate(@Body body: ChosenTime): Single<BaseResponse<DormCheckIn?>>
}