package lc.deck.rudn.data.storage

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.qualifiers.ApplicationContext
import lc.deck.rudn.entity.Roles
import lc.deck.rudn.entity.user.UserAccount
import lc.deck.rudn.ui.cfo_cabinet.CfoCabinetContainerFragment
import lc.deck.rudn.ui.fok.FokContainerFragment
import lc.deck.rudn.ui.iep.elective_disciplines.ElectiveDisciplinesContainerFragment
import lc.deck.rudn.ui.enrollee.study_application.ContractsAndPaymentsFragment
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class Prefs @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    private fun getSharedPreferences(prefsName: String) =
        context.getSharedPreferences(prefsName, Context.MODE_PRIVATE)

    private val AUTH_DATA = "auth_data1"
    private val KEY_USER_ACCOUNT = "ad_accounts"
    private val authPrefs by lazy { getSharedPreferences(AUTH_DATA) }

    private val accountsTypeToken = object : TypeToken<UserAccount>() {}.type
    var account: UserAccount?
        get() = authPrefs
            .getString(KEY_USER_ACCOUNT, null)
            ?.let { gson.fromJson<UserAccount>(it, accountsTypeToken) }
        set(value) {
            if (value != null) {
                authPrefs.edit().putString(KEY_USER_ACCOUNT, gson.toJson(value)).apply()
            } else {
                authPrefs.edit().remove(KEY_USER_ACCOUNT).apply()
            }
        }

    //region app
    private val ROLES_DATA = "roles_data"
    private val KEY_ROLES_DATA = "roles_data"
    private val rolesPrefs by lazy { getSharedPreferences(ROLES_DATA) }

    var roles: Roles?
        get() = rolesPrefs
            .getString(KEY_ROLES_DATA, null)
            ?.let { gson.fromJson<Roles>(it, Roles::class.java) }
        set(value) {
            rolesPrefs.edit().putString(KEY_ROLES_DATA, gson.toJson(value)).apply()
        }

    //region app
    private val APP_DATA = "app_data"
    private val KEY_FIRST_LAUNCH_TIME = "launch_ts"
    private val appPrefs by lazy { getSharedPreferences(APP_DATA) }

    var firstLaunchTimeStamp: Long?
        get() = appPrefs.getLong(KEY_FIRST_LAUNCH_TIME, 0).takeIf { it > 0 }
        set(value) {
            appPrefs.edit().putLong(KEY_FIRST_LAUNCH_TIME, value ?: 0).apply()
        }


    //app settings
    private val SETTINGS_DATA = "settings_data"
    private val settingsPrefs by lazy { getSharedPreferences(SETTINGS_DATA) }

    fun getNotificationEnabled(id: Int): Boolean = settingsPrefs.getBoolean(id.toString(), true)

    fun setNotificationEnabled(id: Int, enabled: Boolean) {
        settingsPrefs.edit().putBoolean(id.toString(), enabled).apply()
    }

    //can user choose group in settings
    private val GROUP_SELECTOR_SETTINGS = "group selector in settings"
    private val groupSelectorInSettingsPref by lazy { getSharedPreferences(GROUP_SELECTOR_SETTINGS) }

    fun setGroupSelectorInSettings(groupSelectorInSettings: Boolean) {
        groupSelectorInSettingsPref.edit().putBoolean(
            GROUP_SELECTOR_SETTINGS,
            groupSelectorInSettings
        ).apply()
    }

    fun getGroupSelectorInSettings(): Boolean =
        groupSelectorInSettingsPref.getBoolean(GROUP_SELECTOR_SETTINGS, true)

    //voice assistant settings
    private val VOICE_ASSISTANT_SETTINGS_DATA = "settings_data"
    private val VOICE = "voice"
    private val voiceAssistantSettingsPrefs by lazy {
        getSharedPreferences(VOICE_ASSISTANT_SETTINGS_DATA)
    }

    fun getVoice(): String? = voiceAssistantSettingsPrefs.getString(VOICE, null)

    fun setVoice(voice: String) {
        voiceAssistantSettingsPrefs.edit().putString(VOICE, voice).apply()
    }

    //group select mode
    private val GROUP_SELECT_MODE_DATA = "settings_data"
    private val IS_GROUP_SELECT_MODE = "voice"
    private val groupSelectModePrefs by lazy {
        getSharedPreferences(GROUP_SELECT_MODE_DATA)
    }

    var groupSelectMode: Boolean
        get() = groupSelectModePrefs.getBoolean(IS_GROUP_SELECT_MODE, false)
        set(value) {
            groupSelectModePrefs.edit().putBoolean(IS_GROUP_SELECT_MODE, value).apply()
        }

    //вкладка ЦФО
    private val CFO_REPORT_ITEM = "cfo report item"
    private val CFO_REPORT_ITEM_VALUE = "cfo report item value"
    private val cfoReportModePrefs by lazy {
        getSharedPreferences(CFO_REPORT_ITEM)
    }

    var cfoReportMode: Int
        get() = cfoReportModePrefs.getInt(
            CFO_REPORT_ITEM_VALUE,
            CfoCabinetContainerFragment.Tabs.AVAILABLE_REPORTS.position
        )
        set(value) {
            cfoReportModePrefs.edit().putInt(CFO_REPORT_ITEM_VALUE, value).apply()
        }

    //вкладка фок
    private val FOK_ITEM = "fok item"
    private val FOK_ITEM_VALUE = "fok item value"
    private val fokModePrefs by lazy {
        getSharedPreferences(FOK_ITEM)
    }

    var fokMode: Int
        get() = fokModePrefs.getInt(
            FOK_ITEM_VALUE,
            FokContainerFragment.Tabs.SPORT_GROUNDS.position
        )
        set(value) {
            fokModePrefs.edit().putInt(FOK_ITEM_VALUE, value).apply()
        }

    //вкладка дисциплины по выбору
    private val ELECTIVE_ITEM = "elective item"
    private val ELECTIVE_ITEM_VALUE = "elective item value"
    private val electiveModePrefs by lazy {
        getSharedPreferences(ELECTIVE_ITEM)
    }

    var electiveMode: Int
        get() = electiveModePrefs.getInt(
            ELECTIVE_ITEM_VALUE,
            ElectiveDisciplinesContainerFragment.Tabs.AVAILABLE.position
        )
        set(value) {
            electiveModePrefs.edit().putInt(ELECTIVE_ITEM_VALUE, value).apply()
        }

    //вкладка договоры и оплаты
    private val CONTRACTS_AND_PAYMENTS_ITEM = "cfo report item"
    private val CONTRACTS_AND_PAYMENTS_ITEM_VALUE = "cfo report item value"
    private val contractsAndPaymentsModePrefs by lazy {
        getSharedPreferences(CONTRACTS_AND_PAYMENTS_ITEM)
    }

    var contractsAndPaymentsMode: Int
        get() = contractsAndPaymentsModePrefs.getInt(
            CONTRACTS_AND_PAYMENTS_ITEM_VALUE,
            ContractsAndPaymentsFragment.Tabs.CONTRACTS.position
        )
        set(value) {
            contractsAndPaymentsModePrefs.edit().putInt(CONTRACTS_AND_PAYMENTS_ITEM_VALUE, value).apply()
        }
}