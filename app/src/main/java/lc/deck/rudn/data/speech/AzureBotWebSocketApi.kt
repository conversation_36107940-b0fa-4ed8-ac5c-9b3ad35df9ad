package lc.deck.rudn.data.speech

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.reactivex.Observable
import lc.deck.rudn.entity.voice_assistant.AzureBotEvent
import okhttp3.*
import timber.log.Timber
import javax.inject.Inject

class AzureBotWebSocketApi @Inject constructor(
    private val gson: Gson,
    okHttpClientBuilder: OkHttpClient.Builder
) {

    private val client = okHttpClientBuilder.build()

    private val typeToken = object : TypeToken<AzureBotEvent>() {}.type

    fun observeSocketEvents(streamUrl: String): Observable<AzureBotWebSocketEvent> =
        Observable.create { emitter ->

            val request = Request.Builder().url(streamUrl).build()
            val listener = object: WebSocketListener() {

                override fun onOpen(webSocket: WebSocket, response: Response) {
                    if (!emitter.isDisposed) {
                        emitter.onNext(AzureBotWebSocketEvent.Connected)
                    }
                }

                override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                    if (!emitter.isDisposed) {
                        emitter.onError(t)
                    }
                }

                override fun onMessage(webSocket: WebSocket, text: String) {
                    try {
                        Timber.d(text)
                        if (text != "") {
                            val event: AzureBotEvent = gson.fromJson(text, typeToken)
                            if (!emitter.isDisposed) {
                                emitter.onNext(AzureBotWebSocketEvent.Message(event))
                            }
                        }
                    } catch (e: Throwable) {
                        Timber.e(e)
                    }
                }

                override fun onClosed(webSocket: WebSocket, code: Int, reason: String?) {
                    if (!emitter.isDisposed) {
                        emitter.onNext(AzureBotWebSocketEvent.Closed(code, reason))
                        emitter.onComplete()
                    }
                }
            }

            val webSocket = client.newWebSocket(request, listener)

            emitter.setCancellable {
                webSocket.close(1001, "cancelled")
            }
        }
}

