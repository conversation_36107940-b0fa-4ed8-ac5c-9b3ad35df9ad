package lc.deck.rudn.data.cache

import lc.deck.rudn.entity.notifications.Notification
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationCache @Inject constructor() {

    private val cacheMap = ConcurrentHashMap<Int?, Notification?>()

    fun put(notificationId: Int?, notification: Notification?) {
        cacheMap[notificationId] = notification
    }

    fun get(notificationId: Int): Notification? {
        return cacheMap[notificationId]
    }

    fun dropCache() {
        cacheMap.clear()
    }
}