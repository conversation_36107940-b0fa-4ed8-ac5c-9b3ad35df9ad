package lc.deck.rudn.data.speech.synthesizer

import com.microsoft.cognitiveservices.speech.audio.PushAudioOutputStreamCallback

class PushOutputStreamCallback : PushAudioOutputStreamCallback() {

    interface Listener {
        fun write(bytes: ByteArray): Int
        fun close()
    }

    var listener: Listener? = null

    override fun write(bytes: ByteArray): Int {
        return listener?.write(bytes) ?: 0
    }

    override fun close() {
        listener?.close()
    }
}