package lc.deck.rudn.data.speech

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.reactivex.Single
import lc.deck.rudn.entity.voice_assistant.Conversation
import lc.deck.rudn.entity.voice_assistant.PostActivityBody
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import timber.log.Timber
import javax.inject.Inject

class AzureBotRestApi @Inject constructor(
    private val gson: Gson,
    okHttpClientBuilder: OkHttpClient.Builder
) {

    private val okHttpClient = okHttpClientBuilder.build()

    private val jsonMediaType = MediaType.parse("application/json; charset=utf-8")

    fun startConversation(token: String): Single<Conversation> = Single.defer<Conversation> {

        val request = Request.Builder()
            .url("https://directline.botframework.com/v3/directline/conversations")
            .addHeader("Authorization", "Bearer $token")
            .method("POST", RequestBody.create(null, ""))
            .build()

        try {
            val response = okHttpClient.newCall(request).execute()
            if (response.isSuccessful) {

                val responseType = object : TypeToken<Conversation>() {}.type

                val conversation =
                    gson.fromJson<Conversation>(
                        response.body()?.charStream(),
                        responseType
                    )

                Timber.d(conversation.toString())

                return@defer Single.just(conversation)

            } else {

                throw RuntimeException("Start conversation error: ${response.code()}")
            }

        } catch (e: Exception) {
            return@defer Single.error(e)
        }
    }

    fun postActivity(conversationId: String, token: String, activity: PostActivityBody): Single<Any> =
        Single.defer<Any> {

            val body = gson.toJson(activity)
            val requestBody = RequestBody.create(jsonMediaType, body)

            val request = Request.Builder()
                .url("https://directline.botframework.com/v3/directline/conversations/$conversationId/activities")
                .addHeader("Authorization", "Bearer $token")
                .post(requestBody)
                .build()

            try {
                val response = okHttpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    return@defer Single.just(Any())
                } else {
                    throw RuntimeException("Start conversation error: ${response.code()}")
                }

            } catch (e: Exception) {
                return@defer Single.error(e)
            }
        }


}