package lc.deck.rudn.data.speech.synthesizer

import android.annotation.SuppressLint
import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.os.Build
import com.microsoft.cognitiveservices.speech.SpeechSynthesisResult
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer
import io.reactivex.Completable
import lc.deck.rudn.system.LocaleHolder
import lc.deck.rudn.system.schedulers.SchedulersProvider
import timber.log.Timber
import java.util.concurrent.Future
import javax.inject.Inject
import javax.inject.Named

class Synthesizer @Inject constructor(
    private val pushOutputStreamCallback: PushOutputStreamCallback,
    private val speechSynthesizer: SpeechSynthesizer,
    @Named("ru") private val speechSynthesizerRu: SpeechSynthesizer,
    private val localeHolder: LocaleHolder,
    private val schedulers: SchedulersProvider
) {

    companion object {
        private const val SAMPLE_RATE = 16000
    }

    private val bufferSize = AudioTrack.getMinBufferSize(
        SAMPLE_RATE,
        AudioFormat.CHANNEL_OUT_MONO,
        AudioFormat.ENCODING_PCM_16BIT
    )

    private val track =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val attributes = AudioAttributes.Builder()
                .setLegacyStreamType(AudioManager.STREAM_MUSIC)
                .build()
            val audioFormat = AudioFormat.Builder()
                .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                .setSampleRate(SAMPLE_RATE)
                .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                .build()
            AudioTrack.Builder()
                .setBufferSizeInBytes(bufferSize)
                .setAudioAttributes(attributes)
                .setAudioFormat(audioFormat)
                .setTransferMode(AudioTrack.MODE_STREAM)
                .build()
        } else {
            @Suppress("DEPRECATION")
            AudioTrack(
                AudioManager.STREAM_MUSIC,
                SAMPLE_RATE,
                AudioFormat.CHANNEL_OUT_MONO,
                AudioFormat.ENCODING_PCM_16BIT,
                bufferSize,
                AudioTrack.MODE_STREAM
            )
        }

    private val listener = object : PushOutputStreamCallback.Listener {
        override fun write(bytes: ByteArray): Int {
            return track.write(bytes, 0, bytes.size)
        }

        override fun close() {
            Timber.d("play audio closed")
            track.release()
        }
    }

    private var pendingSynthesize: Future<SpeechSynthesisResult>? = null

    init {
        pushOutputStreamCallback.listener = listener
    }

    fun synthesize(text: String?, locale: String? = "ru-RU") {
        stopAudio()
        pendingSynthesize?.cancel(false)
        track.play()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            track.setVolume(1f)
        }

        //todo использовать входящий в функцию locale
        val synthesis =
            if (localeHolder.getLocale() == "ru-RU") speechSynthesizerRu.SpeakTextAsync(text) //лучше всего работает через SpeakTextAsync. если запускать в своих тредах, будет жесть
            else speechSynthesizer.SpeakTextAsync(text)
        pendingSynthesize = synthesis
    }

    private fun stopAudio() {
        try {
            track.pause()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                track.setVolume(0f) //при flush/stop издается какой-то мерзкий звук, поэтому такой костыль
            }
            track.flush()
            track.stop()
            pendingSynthesize?.cancel(false)
        } catch (e: IllegalStateException) {
            //ignore
        }
    }

    fun stopSynthesize() {
        stopAudio()
    }

    @SuppressLint("CheckResult")
    fun close() {
        stopAudio()
        pendingSynthesize?.cancel(false)
        track.release()
        pushOutputStreamCallback.listener = null

        //возможна ошибка...
        Completable
            .defer {
                speechSynthesizer.close()
                speechSynthesizerRu.close()
                return@defer Completable.complete()
            }
            .subscribeOn(schedulers.computation())
            .observeOn(schedulers.ui())
            .subscribe(
                { Timber.i("synthesizer closed") },
                { Timber.e(it) }
            )
    }
}