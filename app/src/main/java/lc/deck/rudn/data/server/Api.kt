package lc.deck.rudn.data.server

import io.reactivex.Completable
import io.reactivex.Single
import lc.deck.rudn.entity.BaseResponse
import lc.deck.rudn.entity.BaseResponseSerial
import lc.deck.rudn.entity.NotificationSetting
import lc.deck.rudn.entity.Profile
import lc.deck.rudn.entity.Roles
import lc.deck.rudn.entity.assessment_sheets.AbsenceReasons
import lc.deck.rudn.entity.assessment_sheets.Department
import lc.deck.rudn.entity.assessment_sheets.Period
import lc.deck.rudn.entity.assessment_sheets.Statement
import lc.deck.rudn.entity.assessment_sheets.StatementStatus
import lc.deck.rudn.entity.assessment_sheets.StatementStatusItem
import lc.deck.rudn.entity.assessment_sheets.StatementStudent
import lc.deck.rudn.entity.assessment_sheets.StudentCorrections
import lc.deck.rudn.entity.assessment_sheets.UndergraduateAcademGroup
import lc.deck.rudn.entity.assessment_sheets.requests.Correction
import lc.deck.rudn.entity.assessment_sheets.requests.correction.Approve
import lc.deck.rudn.entity.assessment_sheets.requests.correction.CorrectionStudents
import lc.deck.rudn.entity.assessment_sheets.requests.correction.StatementId
import lc.deck.rudn.entity.assessment_sheets.requests.rejection.TeacherListRequestBody
import lc.deck.rudn.entity.cfo_cabinet.available_reports.AvailableReport
import lc.deck.rudn.entity.cfo_cabinet.form_report.FormReport
import lc.deck.rudn.entity.cfo_cabinet.form_report.ReportSend
import lc.deck.rudn.entity.cfo_cabinet.formed_reports.FormedReports
import lc.deck.rudn.entity.cfo_cabinet.report_departments.ReportDepartments
import lc.deck.rudn.entity.clinical_diagnostic_center.certificates.Certificate
import lc.deck.rudn.entity.clinical_diagnostic_center.vaccines.AddVaccine
import lc.deck.rudn.entity.clinical_diagnostic_center.vaccines.ItemAddVaccineSingleSelector
import lc.deck.rudn.entity.clinical_diagnostic_center.vaccines.Vaccination
import lc.deck.rudn.entity.comments.Comment
import lc.deck.rudn.entity.comments.CommentTree
import lc.deck.rudn.entity.comments.CreateCommentBody
import lc.deck.rudn.entity.comments.PublishCommentBody
import lc.deck.rudn.entity.comments.ReplyCommentBody
import lc.deck.rudn.entity.contact.ContactsData
import lc.deck.rudn.entity.contract_and_payment.contract.Contracts
import lc.deck.rudn.entity.contract_and_payment.invoice.InfoForInvoice
import lc.deck.rudn.entity.contract_and_payment.payer.PayerInfo
import lc.deck.rudn.entity.contract_and_payment.unpaid_bill.UnpaidBills
import lc.deck.rudn.entity.diploma.Diploma
import lc.deck.rudn.entity.diploma.additional_edu_documents.AdditionalEduDocument
import lc.deck.rudn.entity.diploma_confirmation.DiplomaConfirmation
import lc.deck.rudn.entity.diploma_confirmation.DiplomaConfirmationBlocks
import lc.deck.rudn.entity.documents_and_instructions.DocumentContent
import lc.deck.rudn.entity.documents_and_instructions.DocumentTree
import lc.deck.rudn.entity.dormitory.application_info.ApplicationInfo
import lc.deck.rudn.entity.dormitory.application_info.Rejection
import lc.deck.rudn.entity.dormitory.application_info.TypeDormApplication
import lc.deck.rudn.entity.dormitory.applications_list.DormitoryApplication
import lc.deck.rudn.entity.dormitory.apply_form.DormitoryApplyForm
import lc.deck.rudn.entity.dormitory.check_in.CheckInInfo
import lc.deck.rudn.entity.dormitory.check_in.cancel_time.CheckInCancel
import lc.deck.rudn.entity.dormitory.check_in.set_time.CheckInTimeSet
import lc.deck.rudn.entity.dormitory.check_in.time_options.CheckInTimeInfo
import lc.deck.rudn.entity.dormitory.check_list.DormLink
import lc.deck.rudn.entity.dormitory.check_list.DormitorySteps
import lc.deck.rudn.entity.dormitory.config.Config
import lc.deck.rudn.entity.dormitory.contract.ContractFiles
import lc.deck.rudn.entity.dormitory.contract.ContractVerification
import lc.deck.rudn.entity.dormitory.contract.DormitoryContract
import lc.deck.rudn.entity.dormitory.download_file.DownloadFile
import lc.deck.rudn.entity.dormitory.medicine.MedicalExamination
import lc.deck.rudn.entity.dormitory.medicine.RegistrationDates
import lc.deck.rudn.entity.dormitory.personal_data.DormPersonalData
import lc.deck.rudn.entity.dormitory.sign.PhoneNumber
import lc.deck.rudn.entity.email.PersonEmail
import lc.deck.rudn.entity.employee.EduDivision
import lc.deck.rudn.entity.employee.Employee
import lc.deck.rudn.entity.endowment_fund.CapitalPaymentDetail
import lc.deck.rudn.entity.endowment_fund.CapitalPaymentType
import lc.deck.rudn.entity.endowment_fund.EndowmentDescription
import lc.deck.rudn.entity.endowment_fund.EndowmentFundCapital
import lc.deck.rudn.entity.endowment_fund.EndowmentFundContact
import lc.deck.rudn.entity.endowment_fund.EndowmentFundLink
import lc.deck.rudn.entity.endowment_fund.EndowmentFundLinkWeb
import lc.deck.rudn.entity.endowment_fund.PaymentBank
import lc.deck.rudn.entity.enrollee.FileLink
import lc.deck.rudn.entity.enrollee.QrLink
import lc.deck.rudn.entity.enrollee.SubmitAnswer
import lc.deck.rudn.entity.enrollee.application.PersonalDataStatusText
import lc.deck.rudn.entity.enrollee.contract.CodeVerification
import lc.deck.rudn.entity.enrollee.contract.Contract
import lc.deck.rudn.entity.enrollee.contract.ContractPaymentTypeGuid
import lc.deck.rudn.entity.enrollee.contract.FileGuid
import lc.deck.rudn.entity.enrollee.contract.ReportBugRequestBody
import lc.deck.rudn.entity.enrollee.contract.Status
import lc.deck.rudn.entity.enrollee.contract_account.ContractAccount
import lc.deck.rudn.entity.enrollee.enrollee.TranslatorContractCreate
import lc.deck.rudn.entity.enrollee.payment_plan.PaymentPlan
import lc.deck.rudn.entity.enrollee.payment_schedule.PaymentSchedule
import lc.deck.rudn.entity.enrollee.personal_data.EnrolleeData
import lc.deck.rudn.entity.enrollee.personal_data.PhoneNumbers
import lc.deck.rudn.entity.enrollee.sign.ContractSign
import lc.deck.rudn.entity.enrollee.support_description.SupportDescription
import lc.deck.rudn.entity.event.ActivityDirectionParent
import lc.deck.rudn.entity.event.ActivityFormat
import lc.deck.rudn.entity.event.Event
import lc.deck.rudn.entity.event.EventFilter
import lc.deck.rudn.entity.event.Group
import lc.deck.rudn.entity.fok.DirectoryItem
import lc.deck.rudn.entity.fok.abonement.Abonement
import lc.deck.rudn.entity.fok.abonement.AbonementInfo
import lc.deck.rudn.entity.fok.abonement.AbonementRent
import lc.deck.rudn.entity.fok.section.Section
import lc.deck.rudn.entity.fok.section.SectionRegistration
import lc.deck.rudn.entity.fok.sport_grounds.FokBankItem
import lc.deck.rudn.entity.fok.sport_grounds.Playground
import lc.deck.rudn.entity.fok.sport_grounds.PlaygroundRent
import lc.deck.rudn.entity.fok.sport_grounds.PlaygroundRentBody
import lc.deck.rudn.entity.fok.sport_grounds.ScheduleServices
import lc.deck.rudn.entity.fok.sport_grounds.SportGroundSchedule
import lc.deck.rudn.entity.fok.tickets.Tickets
import lc.deck.rudn.entity.grades_book.GradesBookFilter
import lc.deck.rudn.entity.grades_book.GradesBookSemester
import lc.deck.rudn.entity.grades_book.GradesItem
import lc.deck.rudn.entity.group_selection.AcademGroup
import lc.deck.rudn.entity.group_selection.GroupData
import lc.deck.rudn.entity.group_selection.GroupUpdateBody
import lc.deck.rudn.entity.group_selection.StudentGroup
import lc.deck.rudn.entity.iep.body.DisciplinesSignBody
import lc.deck.rudn.entity.iep.body.IepAproveBody
import lc.deck.rudn.entity.iep.discipline.Discipline
import lc.deck.rudn.entity.iep.iep.IndividualEduPlan
import lc.deck.rudn.entity.journal.CreateEventBody
import lc.deck.rudn.entity.journal.JournalDirectoryItem
import lc.deck.rudn.entity.journal.UpdateJournalBody
import lc.deck.rudn.entity.journal.journal.Journal
import lc.deck.rudn.entity.library.Library
import lc.deck.rudn.entity.mdk.filter.MdkFaculty
import lc.deck.rudn.entity.mdk.filter.MdkTheme
import lc.deck.rudn.entity.mdk.subject.CourseSignUp
import lc.deck.rudn.entity.mdk.subject.MdkSubject
import lc.deck.rudn.entity.media.Media
import lc.deck.rudn.entity.medicine.application.cancel.MedicalReturn
import lc.deck.rudn.entity.medicine.application.declined_docs.DeclinedDocuments
import lc.deck.rudn.entity.medicine.application.detail.ApplicationDetail
import lc.deck.rudn.entity.medicine.application.send.DeclinedFilesSend
import lc.deck.rudn.entity.medicine.attachement.Attachment
import lc.deck.rudn.entity.medicine.attachement.send.Answer
import lc.deck.rudn.entity.medicine.attachement.send.SendFilesInfo
import lc.deck.rudn.entity.medicine.decision.Decision
import lc.deck.rudn.entity.medicine.medicine_service.MedicineService
import lc.deck.rudn.entity.medicine.medicine_service.detail.MedicineServiceDetail
import lc.deck.rudn.entity.medicine.payment.ResponseCard
import lc.deck.rudn.entity.medicine.payment.ResponseString
import lc.deck.rudn.entity.medicine.record.CreateRecordRequestBody
import lc.deck.rudn.entity.medicine.record.RecordResponse
import lc.deck.rudn.entity.medicine.record.TimeOption
import lc.deck.rudn.entity.multifunctional_center.DateRange
import lc.deck.rudn.entity.multifunctional_center.FinishedDocuments
import lc.deck.rudn.entity.multifunctional_center.Installment
import lc.deck.rudn.entity.multifunctional_center.MfcCreation
import lc.deck.rudn.entity.multifunctional_center.MfcDate
import lc.deck.rudn.entity.multifunctional_center.MfcDocument
import lc.deck.rudn.entity.multifunctional_center.MfcHistory
import lc.deck.rudn.entity.multifunctional_center.MfcItemService
import lc.deck.rudn.entity.multifunctional_center.MfcSearchResult
import lc.deck.rudn.entity.multifunctional_center.PaymentContracts
import lc.deck.rudn.entity.multifunctional_center.PaymentDate
import lc.deck.rudn.entity.multifunctional_center.PaymentOrder
import lc.deck.rudn.entity.multifunctional_center.PaymentRange
import lc.deck.rudn.entity.multifunctional_center.Signature
import lc.deck.rudn.entity.multifunctional_center.SingleSelectionItem
import lc.deck.rudn.entity.multifunctional_center.preinfo.MfcPreInfo
import lc.deck.rudn.entity.news.News
import lc.deck.rudn.entity.news.NewsCategoryLang
import lc.deck.rudn.entity.notifications.Notification
import lc.deck.rudn.entity.offer.Offer
import lc.deck.rudn.entity.organaizer.AppMapSearch
import lc.deck.rudn.entity.payment.account.AccountReceipt
import lc.deck.rudn.entity.payment.account.AccountReceiptRequestBody
import lc.deck.rudn.entity.payment.bank.Bank
import lc.deck.rudn.entity.payment.bank.QR
import lc.deck.rudn.entity.payment.contract.ContractType
import lc.deck.rudn.entity.payment.contract.PaymentContract
import lc.deck.rudn.entity.payment.invoice.PaymentAcquiringLink
import lc.deck.rudn.entity.payment.invoice.Invoice
import lc.deck.rudn.entity.payment.operation.PaymentOperation
import lc.deck.rudn.entity.payment.print.Print
import lc.deck.rudn.entity.payslip.Faq
import lc.deck.rudn.entity.payslip.LastDate
import lc.deck.rudn.entity.payslip.PersonJob
import lc.deck.rudn.entity.performance.Performance
import lc.deck.rudn.entity.personnel_transactions.DocumentConfirmation
import lc.deck.rudn.entity.personnel_transactions.HrTransaction
import lc.deck.rudn.entity.personnel_transactions.SignDocumentRequestBody
import lc.deck.rudn.entity.personnel_transactions.SignatureAnswer
import lc.deck.rudn.entity.personnel_transactions.SmsCodePin
import lc.deck.rudn.entity.personnel_transactions.SmsCodeVerification
import lc.deck.rudn.entity.personnel_transactions.agreement.AgreementData
import lc.deck.rudn.entity.personnel_transactions.application.ApplicationData
import lc.deck.rudn.entity.personnel_transactions.instruction.Instruction
import lc.deck.rudn.entity.phonebook.PhonebookContact
import lc.deck.rudn.entity.privacy_policy.PrivacyPolicy
import lc.deck.rudn.entity.profile.AbsentPhotoDescription
import lc.deck.rudn.entity.profile.VacationLeftover
import lc.deck.rudn.entity.profile.personal_data.PersonalDataItem
import lc.deck.rudn.entity.profile.personal_data.scientific_activity.Science
import lc.deck.rudn.entity.resources.Resources
import lc.deck.rudn.entity.qr_button.AuditoriumNumber
import lc.deck.rudn.entity.qr_button.LessonBeginEnd
import lc.deck.rudn.entity.qr_button.LessonStarted
import lc.deck.rudn.entity.schedule.Lesson
import lc.deck.rudn.entity.settings.SetNotificationSettingsBody
import lc.deck.rudn.entity.social.SocialNetwork
import lc.deck.rudn.entity.student_check.StudentCheckBody
import lc.deck.rudn.entity.subject.Subject
import lc.deck.rudn.entity.suggestion.Suggestion
import lc.deck.rudn.entity.suggestion.SuggestionRequestBody
import lc.deck.rudn.entity.support.AppealBody
import lc.deck.rudn.entity.support.AppealResponse
import lc.deck.rudn.entity.support.Auditorium
import lc.deck.rudn.entity.support.EstimateRequestBody
import lc.deck.rudn.entity.support.Phone
import lc.deck.rudn.entity.support.Reason
import lc.deck.rudn.entity.support.ReturnToWorkRequestBody
import lc.deck.rudn.entity.support.SupportService
import lc.deck.rudn.entity.theme.Theme
import lc.deck.rudn.entity.undergraduate_academ_group.Faculty
import lc.deck.rudn.entity.user.Pass
import lc.deck.rudn.entity.vacancy.Vacancy
import lc.deck.rudn.entity.vacation_schedule.Balance
import lc.deck.rudn.entity.vacation_schedule.VacationScheduleUser
import lc.deck.rudn.entity.vacation_schedule.VacationStatus
import lc.deck.rudn.ui._global.common.invoice.Payment
import lc.deck.rudn.ui.assessment_sheets.refuse_from_statement.CommentRefuse
import lc.deck.rudn.entity.dormitory.medicine.send.MedicalExaminationSend
import lc.deck.rudn.entity.fok.sport_grounds.Sum
import lc.deck.rudn.entity.portfolio.CollectionItem
import lc.deck.rudn.entity.portfolio.ProfileResponse
import lc.deck.rudn.entity.portfolio.PromotionItem
import lc.deck.rudn.entity.portfolio.SinceActivity
import lc.deck.rudn.entity.profile.personal_data.personnel_data.StudiesAndJobs
import lc.deck.rudn.entity.schedule.Schedule
import lc.deck.rudn.entity.support.AppealItem
import lc.deck.rudn.entity.support.AppealListItem
import lc.deck.rudn.entity.training_reports.discipline_work_program.DisciplineWorkProgram
import lc.deck.rudn.entity.training_reports.teaching_staff.TeachingStaff
import lc.deck.rudn.entity.statements.DismissalStatement
import lc.deck.rudn.entity.statements.DismissalStatementRequest
import lc.deck.rudn.entity.statements.LeaveStatement
import lc.deck.rudn.entity.statements.LeaveStatementRequest
import lc.deck.rudn.entity.statements.RateStatement
import lc.deck.rudn.entity.statements.RateStatementRequest
import lc.deck.rudn.entity.statements.RemotePermanentStatement
import lc.deck.rudn.entity.statements.RemotePermanentStatementRequest
import lc.deck.rudn.entity.statements.RemoteTemporaryStatement
import lc.deck.rudn.entity.statements.RemoteTemporaryStatementRequest
import lc.deck.rudn.entity.statements.StatementReason
import lc.deck.rudn.entity.statements.TransferPositionStatement
import lc.deck.rudn.entity.statements.TransferPositionStatementRequest
import lc.deck.rudn.entity.statements.VacationStatement
import lc.deck.rudn.entity.statements.VacationStatementRequest
import lc.deck.rudn.ui.pass.PassData
import lc.deck.rudn.ui.payment.contracts.entitiy.InvoiceIssue
import lc.deck.rudn.ui.personnel_transactions.statements.SendStatementResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Multipart
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.PartMap
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.QueryMap

interface Api {

    companion object {
        const val API_PATH = "v1"
        const val API_V2_PATH = "v2"
        const val API_V3_PATH = "v3"
        const val API_V1_0_PATH = "v1.0"
    }

    @GET("$API_V2_PATH/event/event-filter")
    fun getEventFilters(): Single<BaseResponse<List<EventFilter>>>

    @GET("$API_PATH/event/event-calendar")
    fun getEventIndicators(
        @QueryMap filters: Map<String?, String?> = mapOf()
    ): Single<BaseResponse<Map<String, Any>>>

    @GET("$API_V2_PATH/service-order/{id}")
    fun getMfcHistoryService(
        @Path("id") id: Int
    ): Single<BaseResponse<MfcHistory>>

    @GET("$API_V2_PATH/child-birthday-type")
    fun getMfcChildBirthdayType(
    ): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V2_PATH/undergraduate-academ-group/program")
    fun getMfcEduProgram(
    ): Single<BaseResponse<List<SingleSelectionItem>>>

    //    @Streaming
    @GET("$API_V2_PATH/service-order-file/download/{id}")
    fun downloadFile(
        @Path("id") id: Int
    ): Single<ResponseBody>

    @GET("$API_V2_PATH/admission-order/list-order")
    fun getAdmissionList(
    ): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V2_PATH/person/preferences")
    fun getRoles(): Single<BaseResponse<Roles>>

    @GET("$API_V2_PATH/service-order-file")
    fun getListOfFiles(): Single<BaseResponse<List<FinishedDocuments>>>

    @GET("$API_V2_PATH/doc-type")
    fun getDocType(
    ): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V2_PATH/invoice-problem-type")
    fun getProblemTypes(
    ): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V2_PATH/graduate-intern")
    fun getGraduateType(
    ): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V2_PATH/performance/filter?filter[order_source_id]=1")
    fun getGradesFilters(
        @Query("filter[undergraduate_academ_group_id]") academicGroupId: Int?
    ): Single<BaseResponse<List<GradesBookFilter>>>

    @GET("$API_V2_PATH/performance")
    fun getGrades(
        @Query("filter[academ_year_id]") academicYearId: Int,
        @Query("filter[semestr_id]") semesterId: Int?,
        @Query("filter[final_control_form_type_id]") finalControlFormId: Int?
    ): Single<BaseResponse<List<GradesItem>>>


    @GET("$API_V2_PATH/service-order/reference/{id}")
    fun getMfcHistoryReference(
        @Path("id") id: Int
    ): Single<BaseResponse<MfcHistory>>

    @GET("$API_PATH/event")
    fun getEvents(
        @Query("expand") event: String = "eventWorkProgramSection.workProgramSection.workshopType,eventWorkProgramSection.eventWorkProgramSectionGroups.academGroup.studyGroups,eventWorkProgramSection.workProgramSection.workProgram.workProgramCurriculumSubjs.curriculumSubject,eventWorkProgramSection.eventWorkProgramSectionEmployees.employee.user.person.file,room.territory,eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventActivity.activity.activityFormat,eventActivity.activity.activityOrganizer,eventActivity.activity.activityType,eventActivity.activity.activityDirections,eventActivity.activity.activityDirectionParents,eventActivity.activity.activityContactPersonRudn,eventService.service.serviceOrder",
        @Query("filter[datetime_start]") dateStart: String,
        @Query("filter[datetime_end]") dateEnd: String,
        @QueryMap filters: Map<String?, String?> = mapOf()
    ): Single<BaseResponse<List<Event>>>

    @GET("$API_PATH/event")
    fun getEventsPaging(
        @Query("expand") event: String = "eventWorkProgramSection.workProgramSection.workshopType,eventWorkProgramSection.eventWorkProgramSectionGroups.academGroup.studyGroups,eventWorkProgramSection.workProgramSection.workProgram.workProgramCurriculumSubjs.curriculumSubject,eventWorkProgramSection.employee.user.person.file,room.territory,eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventActivity.activity.activityFormat,eventActivity.activity.activityOrganizer,eventActivity.activity.activityType,eventActivity.activity.activityDirections,eventActivity.activity.activityDirectionParents,eventActivity.activity.activityContactPersonRudn",
        @Query("filter[datetime_start]") dateStart: String,
        @Query("filter[datetime_end]") dateEnd: String,
        @Query("page") page: Int,
        @Query("per-page") perPage: Int,
        @QueryMap filters: Map<String?, String?> = mapOf()
    ): Single<BaseResponse<List<Event>>>

    @GET("$API_V2_PATH/activity")
    fun getEventsUnauthorized(
        @Query("expand") event: String = "eventWorkProgramSection.workProgramSection.workshopType,eventWorkProgramSection.eventWorkProgramSectionGroups.academGroup.studyGroups,eventWorkProgramSection.workProgramSection.workProgram.workProgramCurriculumSubjs.curriculumSubject,eventWorkProgramSection.eventWorkProgramSectionEmployees.employee.user.person.file,room.territory,eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventActivity.activity.activityFormat,eventActivity.activity.activityOrganizer,eventActivity.activity.activityType,eventActivity.activity.activityDirections,eventActivity.activity.activityDirectionParents,eventActivity.activity.activityContactPersonRudn",
        @Query("filter[datetime_start]") dateStart: String,
        @Query("filter[datetime_end]") dateEnd: String,
        @Query("page") page: Int,
        @Query("per-page") perPage: Int,
        @QueryMap filters: Map<String?, String?> = mapOf()
    ): Single<BaseResponse<List<Event>>>


    @GET("$API_V2_PATH/news")
    fun getNewsList(
        @Query("page") page: Int,
        @Query("per-page") perPage: Int,
        @Query("filter[news_category_id]") categoryFilter: String
    ): Single<BaseResponse<List<News>>>

    @GET("/v2/media/current")
    fun getDailyMedia(): Single<BaseResponse<Media>>

    @GET("/v3/social-media")
    fun getSocialMedias(): Single<BaseResponse<List<SocialNetwork>>>

    @Multipart
    @POST("$API_V2_PATH/service-order/receive/{id}")
    fun sendDateTimeMfc(
        @Path("id") id: Int,
        @Part("service[service_type_id]") serviceId: Int?,
        @Part("service[order_source_id]") serviceSourceId: Int?,
        @Part("service[date]") serviceDate: RequestBody?,
        @Part("service[plan_time]") servicePlanTime: RequestBody?
    ): Single<BaseResponse<MfcHistory>>

    @GET("/v2/service-order/params/{id}")
    fun getMfcAppCreation(
        @Path("id") id: Int
    ): Single<BaseResponse<MfcCreation>>

    @GET("/v2/contract")
    fun getContracts(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/duplicate-reason")
    fun getDuplicateReasons(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/return-reason/return-reason-list")
    fun getReturnReasons(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/mvd")
    fun getMvdList(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("/v2/contract/year/{id}")
    fun getMfcYear(
        @Path("id") id: Int
    ): Single<BaseResponse<List<Int>>>

    @GET("$API_PATH/news-category")
    fun getNewsCategoryList(): Single<BaseResponse<List<NewsCategoryLang>>>

    @GET("$API_PATH/event/{id}")
    fun getEvent(
        @Path("id") id: Int,
        @Query("expand") event: String = "eventWorkProgramSection.workProgramSection.workshopType,eventWorkProgramSection.eventWorkProgramSectionGroups.academGroup.studyGroups,eventWorkProgramSection.workProgramSection.workProgram.workProgramCurriculumSubjs.curriculumSubject,eventWorkProgramSection.eventWorkProgramSectionEmployees.employee.user.person.file,,room.territory,eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventActivity.activity.activityFormat,eventActivity.activity.activityOrganizer,eventActivity.activity.activityType,eventActivity.activity.activityDirections,eventActivity.activity.activityDirectionParents,eventActivity.activity.activityContactPersonRudn"
    ): Single<BaseResponse<Event>>

    @GET("$API_V2_PATH/activity/{id}")
    fun getEventUnauthorized(
        @Path("id") id: Int,
        @Query("expand") event: String = "eventWorkProgramSection.workProgramSection.workshopType,eventWorkProgramSection.eventWorkProgramSectionGroups.academGroup.studyGroups,eventWorkProgramSection.workProgramSection.workProgram.workProgramCurriculumSubjs.curriculumSubject,eventWorkProgramSection.eventWorkProgramSectionEmployees.employee.user.person.file,,room.territory,eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventActivity.activity.activityFormat,eventActivity.activity.activityOrganizer,eventActivity.activity.activityType,eventActivity.activity.activityDirections,eventActivity.activity.activityDirectionParents,eventActivity.activity.activityContactPersonRudn"
    ): Single<BaseResponse<Event>>

    @POST("$API_PATH/event/toggle-favorite/{id}")
    fun toggleFavorite(
        @Path("id") id: Int
    ): Single<BaseResponse<Boolean?>>

    @GET("$API_V3_PATH/person/me")
    fun getProfile(
        @Query("expand") expand: String = "file,sex,privateDocVisa,privateDocRegistration,privateDocMigration,academicTitle,degree,employeePositions.position,employeePositions.department.faculty,user.person.personEmail,user.person.personTelephones,user.person.file,academGroup.faculty,undergraduateStatusType,curriculum.educationFormLevelLength,course,privateDocs,employeePositions.subdivision,personEmails"
    ): Single<BaseResponse<Profile>>

    @GET("$API_PATH/notification")
    fun getNotifications(): Single<BaseResponse<List<Notification?>?>>

    @GET("/v2/service/gap/{id}")
    fun getDates(
        @Path("id") id: Int,
        @Query("service_id") serviceId: Int
    ): Single<BaseResponse<List<MfcDate>>>

    @GET("$API_PATH/notification")
    fun getNotificationsNew(
        @Query("expand") expand: String = "fromPerson.file"
    ): Single<BaseResponse<List<Notification?>?>>

    @GET("$API_PATH/employee/{id}")
    fun getTeacher(
        @Path("id") id: Int,
        @Query("expand") expand: String = "user.person.file,user.person.personEmail,user.person.personTelephones,academicTitle,degree,employeePositions.position,employeePositions.department.faculty"
    ): Single<BaseResponse<Employee>>

    @GET("$API_PATH/event")
    fun getSubjectPlan(
        @Query("expand") expand: String = "eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventWorkProgramSection.workProgramSection,personEventVisited,countOfVisitorsActivity,isVisited,countParentComments,is_noted,is_noted_all",
        @Query("filter[work_program_id]") workProgramId: Int,
        @Query("filter[academ_group]") groupId: Int?,
        @Query("filter[undergraduate_academ_group_id]") student_id: Int?,
        @Query("filter[is_visited]") isVisits: Boolean?,
        @Query("filter[is_noted]") isNoted: Int?
    ): Single<BaseResponse<List<Event>>>

    @POST("/v2/person/delete-image")
    fun deleteAvatar(): Single<Any>

    @Multipart
    @POST("$API_V2_PATH/person/upload-image")
    fun uploadAvatar(
        @Part file: MultipartBody.Part
    ): Single<Any>

    @POST("$API_PATH/notification-settings-person/set")
    fun setNotificationSettings(
        @Body setNotificationSettingsBody: SetNotificationSettingsBody
    ): Single<Any>

    @GET("$API_PATH/notification-settings-person")
    fun getNotificationsSettings(
        @Query("expand") expand: String = "notificationCategory",
        @Query("person_id") personId: Int?
    ): Single<BaseResponse<List<NotificationSetting>>>

    @GET("$API_PATH/performance")
    fun getPerformance(
        @Query("expand") expand: String = "curriculumSubject.subject,curriculumSubject.semestr.course,curriculumSubject.finalControlForm,employee.person",
        @Query("academ_group_id") groupId: Int
    ): Single<BaseResponse<Performance>>

    @GET("$API_PATH/event/search")
    fun searchEventSuggestions(
        @Query("filter[strict]") strict: Int = 0,
        @Query("filter[search]") searchQuery: String,
        @Query("filter[event_type_id]") eventTypeId: String,
        @Query("filter[datetime_start]") datetimeStart: String,
        @Query("filter[favorite]") isFavorite: Int
    ): Single<BaseResponse<List<String?>>>

    @GET("$API_PATH/event")
    fun searchEvents(
        @Query("filter[strict]") strict: Int = 0,
        @Query("expand") event: String = "eventWorkProgramSection.workProgramSection.workshopType,eventWorkProgramSection.eventWorkProgramSectionGroups.academGroup.studyGroups,eventWorkProgramSection.workProgramSection.workProgram.workProgramCurriculumSubjs.curriculumSubject,eventWorkProgramSection.employee.user.person.file,room.territory,eventWorkProgramSection.workProgramSection.workProgram.parentWorkshopType,eventActivity.activity.activityFormat,eventActivity.activity.activityOrganizer,eventActivity.activity.activityType,eventActivity.activity.activityDirections,eventActivity.activity.activityDirectionParents,eventActivity.activity.activityContactPersonRudn",
        @Query("filter[datetime_start]") dateStart: String,
        @Query("filter[datetime_end]") dateEnd: String,
        @Query("filter[search]") searchQuery: String?,
        @Query("filter[event_type_id") eventTypeId: Int,
        @Query("filter[room_id]") roomId: String?
    ): Single<BaseResponse<List<Event>>>

    @GET("/v2/service")
    fun getMfcDocuments(
        @Query("filter[service_category_id]") id: Int
    ): Single<BaseResponse<List<MfcDocument>>>

    @GET("/v2/service-order")
    fun getReferenceHistoryList(
    ): Single<BaseResponse<List<MfcHistory>>>

    @GET("$API_PATH/event/academ-groups")
    fun getSubject(
        @Query("event_id") eventId: Int
    ): Single<BaseResponse<List<Group>>>

    @GET("$API_PATH/service-templates")
    fun getServicePreInfo(
        @Query("filter[service_id]") id: Int
    ): Single<BaseResponse<List<MfcPreInfo>?>>

    @GET("/v2/service-category")
    fun getMfcServiceList(): Single<BaseResponse<List<MfcItemService>>>

    @PATCH("$API_PATH/person-event-visited/update")
    fun updateStudentsAttendance(
        @Body studentCheckBody: StudentCheckBody
    ): Single<BaseResponse<Any?>>

    @GET("$API_PATH/person/firebase-token")
    fun registerFCMToken(
        @Query("token") token: String?
    ): Single<BaseResponse<Any>>

    @GET("$API_PATH/auth/logout")
    fun logout(): Single<BaseResponse<Boolean>>

    @GET("$API_PATH/notification/{id}")
    fun readNotification(
        @Path("id") id: Int
    ): Single<BaseResponse<Notification>>

    @GET("$API_PATH/event-comment")
    fun getEventComments(
        @Query("filter[event_id]") eventId: Int,
        @Query("expand") expand: String = "fromPerson,toPerson,parentEventCommentRelations.eventComment.fromPerson,parentEventCommentRelations.eventComment.toPerson, fromPerson.file,toPerson.file"
    ): Single<BaseResponse<List<CommentTree>>>

    @GET("$API_PATH/event-comment")
    fun getCommentTree(
        @Query("filter[parent_event_comment_id]") parentCommentId: Int,
        @Query("expand") expand: String = "fromPerson,toPerson,parentEventCommentRelations.eventComment.fromPerson,parentEventCommentRelations.eventComment.toPerson,fromPerson.file"
    ): Single<BaseResponse<List<Comment>>>

    @POST("$API_PATH/event-comment/create")
    fun createComment(
        @Body createCommentBody: CreateCommentBody,
        @Query("expand") expand: String = "fromPerson,toPerson,parentEventCommentRelations.eventComment.fromPerson,parentEventCommentRelations.eventComment.toPerson,fromPerson.file"
    ): Single<BaseResponse<Comment>>

    @POST("$API_PATH/event-comment/reply/{id}")
    fun replyComment(
        @Path("id") commentId: Int,
        @Body replyCommentBody: ReplyCommentBody,
        @Query("expand") expand: String = "fromPerson,toPerson,parentEventCommentRelations.eventComment.fromPerson,parentEventCommentRelations.eventComment.toPerson,fromPerson.file"
    ): Single<BaseResponse<Comment>>

    @DELETE("$API_V3_PATH/event-comment/{id}")
    fun deleteComment(
        @Path("id") commentId: Int
    ): Completable

    @PUT("$API_PATH/event-comment/{id}")
    fun makeCommentPublic(
        @Path("id") commentId: Int,
        @Query("expand") expand: String = "fromPerson.smallFile",
        @Body publishCommentBody: PublishCommentBody
    ): Single<BaseResponse<Comment>>

    @GET("$API_V3_PATH/person/generate-pass")
    fun generatePass(): Single<BaseResponse<PassData>>

    @GET("$API_PATH/activity-format")
    fun getActionFormats(): Single<BaseResponse<List<ActivityFormat>>>

    @GET("$API_PATH/activity-direction-parent")
    fun getActionDirections(): Single<BaseResponse<List<ActivityDirectionParent>>>

    @GET("$API_V2_PATH/undergraduate-academ-group")
    fun getMyGroups(): Single<BaseResponse<GroupData>>

    @GET("$API_PATH/academ-group/external")
    fun getAllGroups(): Single<BaseResponse<List<AcademGroup>>>

    @Multipart()
    @POST("$API_V2_PATH/service-order/create")
    fun createApplicationOrder(
        @Part("referenceOrder[reference_id]") referenceId: Int?,
        @Part("referenceOrder[is_paper]") isPaper: Int?,
        @Part("referenceOrder[count]") count: Int?,
        @Part("referenceOrder[is_ecp]") isDigital: Int?,
        @Part("referenceOrder[is_dismission]") isDismission: Int?,
        @Part("referenceOrder[fio_latin]") fioLatin: RequestBody?,
        @Part("referenceOrder[fio_child]") fioChild: RequestBody?,
        @Part("referenceOrder[child_birthday]") childDate: RequestBody?,
        @Part("referenceOrder[child_birthday_type_id]") childBirthDayType: Int?,
        @Part("referenceOrder[graduate_intern_id]") graduateType: Int?,
        @Part("referenceOrder[admission_order_id]") admissionOrderId: Int?,
        @Part("referenceOrder[doc_type_id]") docType: Int?,
        @Part("referenceOrder[place_of_demand]") placeOfDemand: RequestBody?,
        @Part("referenceOrder[education_program_parent_id]") educationProgramId: Int?,
        @Part("referenceOrder[invoice_problem_type_id]") problemType: Int?,
        @Part("referenceOrder[reference_order_academ_year][]") academicYears: ArrayList<Int>?,
        @Part("referenceOrder[order_source_id]") referenceSourceId: Int?,
        @Part files: List<MultipartBody.Part?>?,
        @Part("service[service_type_id]") serviceId: Int?,
        @Part("service[order_source_id]") serviceSourceId: Int?,
        @Part("service[date]") serviceDate: RequestBody?,
        @Part("service[plan_time]") servicePlanTime: RequestBody?,
        @Part("contract_id") contractId: Int?,
        @Part("referenceOrder[privacy_policy]") privacyPolicy: Int?,
        @Part("referenceOrder[duplicate_reason_id]") duplicateReasonId: Int?,
        @Part("referenceOrder[period_start]") periodStart: RequestBody?,
        @Part("referenceOrder[period_end]") periodEnd: RequestBody?,
        @Part("referenceOrder[calendar_year]") calendarYear: Int?,
        @Part("applicationOrder[return_reason_id]") returnReasonId: Int?,
        @Part("referenceOrder[mvd_id]") mvdId: Int?,
        @Part("applicationOrder[exam_scores]") examScores: Int?,
        @Part("applicationOrder[motherData][fio]") motherFio: RequestBody?,
        @Part("applicationOrder[motherData][residential_address]") motherAddress: RequestBody?,
        @Part("applicationOrder[motherData][place_of_work]") motherPlaceOfWork: RequestBody?,
        @Part("applicationOrder[fatherData][fio]") fatherFio: RequestBody?,
        @Part("applicationOrder[fatherData][residential_address]") fatherAddress: RequestBody?,
        @Part("applicationOrder[fatherData][place_of_work]") fatherPlaceOfWork: RequestBody?,
        @Part("applicationOrder[application_id]") applicationId: Int?,
        @Part("applicationOrder[order_source_id]") applicationSourceId: Int?,
        @Part("applicationOrder[requisites][bank_name]") bankName: RequestBody?,
        @Part("applicationOrder[requisites][correspondent_bank_account]") correspondentBankAccount: RequestBody?,
        @Part("applicationOrder[requisites][tax_number_bank]") taxNumberBank: RequestBody?,
        @Part("applicationOrder[requisites][beneficiary_account]") beneficiaryAccount: RequestBody?,
        @Part("applicationOrder[requisites][name_of_recipient]") nameOfRecipient: RequestBody?,
        @Part("applicationOrder[requisites][bic_bank]") bicBank: RequestBody?,
        @Part("applicationOrder[requisites][ppc_bank]") ppcBank: RequestBody?,
        @Part("applicationOrder[free_input_field]") freeInputField: RequestBody?,
        @Part("applicationOrder[discount_reason_id]") discountReasonId: Int?,
        @Part("applicationOrder[payment_option_id]") paymentOptionId: Int?,
        @PartMap paymentSumDate: HashMap<String, RequestBody>?,
        @PartMap paymentSumSum: HashMap<String, Number>?,
        @Part("applicationOrder[education_programm_type]") educationProgrammTypeId: Int?,
        @Part("applicationOrder[education_programm_benefits]") educationProgramBenefitId: Int?,
        @Part("date") needDate: RequestBody?,
        @Part("reason_call_id") reasonCallId: Int?,
        @Part("applicantCategoryIds[][id]") applicantCategoryIds: List<Int>?,
        @Part("referenceOrder[military_document_type_id]") militaryDocumentTypeId: Int?,
        @Part("referenceOrder[stock_categories_id]") stockCategoriesId: Int?,
        @Part("referenceOrder[military_personnel_id]") militaryPersonnelId: Int?,
        @Part("referenceOrder[military_rank_id]") militaryRankId: Int?,
        @Part("referenceOrder[category_of_fitness_id]") categoryOfFitnessId: Int?,
        @Part("referenceOrder[military_enlistment_office_id]") militaryEnlistmentOfficeId: Int?,
        @Part("referenceOrder[attitudes_military_service_id]") attitudesMilitaryServiceId: Int?,
        @Part("referenceOrder[number_vus]") numberVus: RequestBody?,
        @Part("referenceOrder[military_document_series]") militaryDocumentSeries: RequestBody?,
        @Part("referenceOrder[military_document_number]") militaryDocumentNumber: RequestBody?,
        @Part("referenceOrder[name_education]") nameEducation: RequestBody?,
        @Part("referenceOrder[name_education_specialty]") nameEducationSpeciality: RequestBody?,
        @Part("referenceOrder[citizenship_id]") citizenshipId: Int?,
        @Part("referenceOrder[sex_id]") sexId: Int?,
        @Part("referenceOrder[education_level_id]") educationLevelId: Int?,
        @Part("referenceOrder[identity_card_id]") identityCard: Int?,
        @Part("referenceOrder[identity_card_issued_by]") identityCardIssuedBy: RequestBody?,
        @Part("referenceOrder[birthplace]") birthplace: RequestBody?,
        @Part("referenceOrder[registry]") registry: RequestBody?,
        @Part("referenceOrder[identity_card_series]") identityCardSeries: Int?,
        @Part("referenceOrder[identity_card_number]") identityCardNumber: RequestBody?,
        @Part("referenceOrder[identity_card_issue_date]") identityCardIssueDate: RequestBody?,
        @Part("referenceOrder[birthdate]") birthdate: RequestBody?,
        @PartMap paymentNumber: HashMap<String, RequestBody>?,
        @PartMap paymentDate: HashMap<String, RequestBody>?,
        @Part("position_id") workingPositionId: Int?,
        @Part("referenceOrder[separate_name]") separateName: RequestBody?,
        @Part("referenceOrder[separate_surname]") separateSurname: RequestBody?,
        @Part("referenceOrder[separate_patronymic]") separatePatronymic: RequestBody?,
        @Part("referenceOrder[separate_name_lat]") separateNameLat: RequestBody?,
        @Part("referenceOrder[separate_surname_lat]") separateSurnameLat: RequestBody?,
        @Part("referenceOrder[separate_patronymic_lat]") separatePatronymicLat: RequestBody?,
        @Part("referenceOrder[date]") date: RequestBody?,
        @Part("referenceOrder[reason_shift_fio_id]") reasonShiftFioId: Int?,
        @Part("referenceOrder[achievement_type_id]") achievementTypeId: Int?,
        @Part("referenceOrder[achievement_category_id]") achievementCategoryId: Int?,
        @Part("referenceOrder[achievement_level_id]") achievementLevelId: Int?,
        @Part("referenceOrder[academic_year_id]") academicYearId: Int?,
        @Part("referenceOrder[contract_id]") contractIdReference: Int?,
        @Part("referenceOrder[currency_id]") currencyId: Int?,
        @Part("referenceOrder[on_contract_id]") onContractId: Int?,
        @Part("referenceOrder[reason_transfer_id]") reasonTransferId: Int?,
        @Part("referenceOrder[semester_id]") semesterId: Int?,
        @Part("referenceOrder[transfer_option_id]") transferOptionId: Int?,
        @Part("referenceOrder[driving_program_id]") drivingProgramId: Int?,
        @Part("referenceOrder[education_type_id]") educationTypeId: Int?,
        @Part("referenceOrder[work_place]") workPlace: RequestBody?,
        @Part("referenceOrder[driving_certificate_category_id]") drivingCertificateCategoryId: Int?,
        @Part("referenceOrder[driving_certificate_category_presence]") drivingCertificateCategoryPresence: Int?,
        @Part("referenceOrder[legalRepresentative][snils]") snilsLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][name]") nameLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][phone_number]") phoneNumberLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][email]") emailLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][birthdate]") birthdateLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][identity_card_series]") identityCardSeriesLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][identity_card_number]") identityCardNumberLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][identity_card_issued_by]") identityCardIssuedByLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][identity_card_issue_date]") identityCardIssueDateLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][birthplace]") birthplaceLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][identity_card_living_place]") identityCardLivingPlaceLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][temporal_registration_living_place]") temporalRegistrationLivingPlaceLR: RequestBody?,
        @Part("referenceOrder[legalRepresentative][sex_id]") sexIdLR: Int?,
        @Part("referenceOrder[personal_data_confirm]") personalDataConfirm: Int?,
        @Part("referenceOrder[education_level_extended_id]") educationLevelExtendedId: Int?,
        @Part("referenceOrder[faculty_id]") facultyId: Int?,
        @Part("referenceOrder[finance_type_id]") financeTypeId: Int?,
        @Part("referenceOrder[payment_period_id]") paymentPeriodId: Int?,
        @Part("referenceOrder[speciality_id]") specialityId: Int?,
        @Part("referenceOrder[client_temporary_address]") clientTemporaryAddress: RequestBody?,
        @Part("referenceOrder[mother_temporary_address]") motherTemporaryAddress: RequestBody?,
        @Part("referenceOrder[father_temporary_address]") fatherTemporaryAddress: RequestBody?,
        @Part("referenceOrder[birth_city]") birthCity: RequestBody?,
        @Part("referenceOrder[reason_budget_id]") reasonBudgetId: Int?,
        @Part("referenceOrder[archive_visit_reason_id]") archiveVisitReasonId: Int?,
        @Part("referenceOrder[visit_time]") visitTime: RequestBody?,
        @Part("applicationOrder[date_agenda]") dateAgenda: RequestBody?,
        @Part("referenceOrder[date_reference]") dateReference: RequestBody?,
        @Part("referenceOrder[week_pregnancy]") weekPregnancy: RequestBody?,
        @Part("referenceOrder[allow_visit]") allowVisit: Int?,
        @PartMap birthCertificatesBirthdate: HashMap<String, RequestBody>?,
        @PartMap birthCertificatesDate: HashMap<String, RequestBody>?,
        @PartMap birthCertificatesFio: HashMap<String, RequestBody>?,
        @PartMap birthCertificatesSeries: HashMap<String, RequestBody>?,
        @PartMap birthCertificatesNumber: HashMap<String, RequestBody>?,
        @Part("referenceOrder[faculty_archive]") facultyArchive: RequestBody?,
        @Part("referenceOrder[speciality_archive]") specialityArchive: RequestBody?,
        @Part("referenceOrder[year_archive]") yearArchive: RequestBody?,
        @Part("referenceOrder[nld_archive]") nldArchive: RequestBody?,
    ): Single<BaseResponse<MfcHistory>>

    @POST("$API_PATH/academ-group")
    fun addGroup(
        @Body body: GroupUpdateBody
    ): Single<BaseResponse<GroupData>>

    @PUT("$API_V2_PATH/undergraduate-academ-group/{undergraduateGroupId}")
    fun updateGroup(
        @Path("undergraduateGroupId") undergraduateGroupId: Int,
        @Body groupUpdateBody: GroupUpdateBody
    ): Single<BaseResponse<StudentGroup>>

    @GET("$API_V2_PATH/topic")
    fun getThemesList(): Single<BaseResponse<List<Theme>>>

    @GET("$API_V2_PATH/person/emails")
    fun getEmailsList(): Single<BaseResponse<List<PersonEmail>>>

    @Multipart
    @POST("$API_V2_PATH/open-dialog/attach/{id}")
    fun uploadFile(
        @Path("id") suggestionId: Int,
        @Part file: MultipartBody.Part
    ): Single<Any>

    @POST("$API_V2_PATH/open-dialog")
    fun sendSuggestion(@Body suggestionRequestBody: SuggestionRequestBody): Single<BaseResponse<Suggestion>>

    @GET("$API_V2_PATH/offer")
    fun getOffersList(): Single<BaseResponse<List<Offer?>?>>

    @GET("$API_V2_PATH/phonebook/contact-phone")
    fun getContactsData(
        @Query("filter[search]") search: String?
    ): Single<BaseResponse<ContactsData?>>

    @GET("$API_V2_PATH/person/list-of-subjects")
    fun getSubjectList(): Single<BaseResponse<List<Subject>>>

    @GET("$API_V2_PATH/work-program/list-groups")
    fun getJournalInfo(
        @Query("filter[work_program_id]") subject_id: Int?,
        @Query("filter[faculty_id]") facultyId: Int?,
        @Query("filter[student]") studentQuery: String?,
        @Query("filter[group]") groupQuery: String?,
        @Query("filter[academ_group_id]") groupId: Int?
    ): Single<BaseResponse<Subject?>>

    @GET("$API_V2_PATH/academ-group/summary-analytic")
    fun getStudentByQuery(
        @Query("work_program_id") subject_id: Int?,
        @Query("academ_group_id") facultyId: Int?,
        @Query("full_name") studentQuery: String?
    ): Single<BaseResponse<Subject?>>

    @GET("$API_V2_PATH/academ-group/summary-analytic")
    fun getGroupDetails(
        @Query("academ_group_id") group_id: Int,
        @Query("work_program_id") subject_id: Int
    ): Single<BaseResponse<lc.deck.rudn.entity.subject.Group>>

    @GET("$API_V2_PATH/academ-group/summary-analytic")
    fun getStudent(
        @Query("work_program_id") subject_id: Int,
        @Query("undergraduate_academ_group_id") student_id: Int
    ): Single<BaseResponse<lc.deck.rudn.entity.subject.Group>>

    @GET("$API_V2_PATH/performance/filter")
    fun getGradesBookSemester(): Single<BaseResponse<List<GradesBookSemester>>>

    @GET("$API_V2_PATH/academ-group/list-faculties")
    fun getInstitutes(@Query("work_program_id") programId: Int): Single<BaseResponse<List<Faculty>>>

    @GET("diploma-confirmation/$API_PATH/diploma")
    fun getDiplomasForConfirm(): Single<BaseResponse<List<DiplomaConfirmation?>?>>

    @GET("diploma-confirmation/v1/block")
    fun getDiplomaConfirmBlocks(
        @Query("block_id") diplomaId: String
    ): Single<BaseResponse<DiplomaConfirmationBlocks>>

    @GET("diploma-confirmation/v1/block/file")
    fun getDiplomaBlockFile(
        @Query("fileID") fileId: String?
    ): Single<ResponseBody?>

    @POST("diploma-confirmation/v1/block/update")
    fun sendDiplomaConfirmation(
        @Body diplomaConfirmation: DiplomaConfirmationBlocks
    ): Single<BaseResponse<DiplomaConfirmationBlocks>>

    @GET("portfolio/$API_PATH/profile")
    fun getProfile(
        @Query("personID") personID: String,
        @Query("academGroupID") academGroupID: String,
        @Query("periodID") periodID: String,
    ): Single<BaseResponse<ProfileResponse>>

    @GET("portfolio/$API_PATH/scientific-activities")
    fun getSinceActivity(
        @Query("personID") personID: String,
        @Query("academGroupID") academGroupID: String,
        @Query("periodID") periodID: String,
        @Query("sortDate") sort: String,
        //@Query ("search") search: String? = null,
    ): Single<BaseResponse<SinceActivity>>

    @GET("portfolio/$API_PATH/promotions")
    fun getPromotions(
        @Query("personID") personID: String,
        @Query("academGroupID") academGroupID: String,
        @Query("periodID") periodID: String,
        @Query("sortDate") sort: String,
        // @Query ("search") search: String? = null,
    ): Single<BaseResponse<List<PromotionItem>>>

    @GET("portfolio/$API_PATH/collections")
    fun getCollections(
        @Query("personID") personID: String,
        @Query("academGroupID") academGroupID: String,
        @Query("periodID") periodID: String,
        @Query("sortDate") sort: String,
        // @Query ("search") search: String? = null,
    ): Single<BaseResponse<List<CollectionItem>>>

    @GET("portfolio/$API_PATH/directory/period")
    fun getDirectoryPeriod(
        @Query("personID") personId: String,
        @Query("academGroupID") academGroupId: String
    ): Single<BaseResponseSerial<List<DirectoryItem>?>>

    @GET("$API_V3_PATH/diploma-list")
    fun getDiplomasList(): Single<BaseResponse<List<Diploma?>?>>

    @GET("$API_V3_PATH/diploma")
    fun getDiplomaInfo(
        @Query("diploma_guid") diplomaGuid: String?
    ): Single<BaseResponse<Diploma?>>

    @GET("payment/contract-type")
    fun getContactTypes(): Single<BaseResponse<List<ContractType?>?>>

    @GET("payment/contract")
    fun getContractsByType(
        @QueryMap params: Map<String, String>
    ): Single<BaseResponse<List<PaymentContract?>?>>

    @GET("payment/invoice-get")
    fun getInvoice(
        @Query("accountNum") accountNum: String?
    ): Single<BaseResponse<lc.deck.rudn.entity.payment.invoice.Invoice>>

    @GET("payment/invoice-get-qr")
    fun payByQr(@Query("accountNum") accountNum: String): Single<BaseResponse<QR?>>

    @POST("payment/account")
    fun issueInvoice(@Query("guid") guid: String?, @Body body: InvoiceIssue): Completable

    @GET("payment/invoice-get-print")
    fun getInvoicePrint(@Query("accountNum") guid: String?): Single<BaseResponse<Print?>>

    @POST("payment/links")
    fun getBankLinks(@Body payment: Payment): Single<BaseResponse<List<lc.deck.rudn.entity.payment.bank.Bank?>?>>

    @GET("payment/contract-pay/list")
    fun getOperationByGuid(
        @Query("guid") guid: String?,
    ): Single<BaseResponse<List<PaymentOperation?>?>>

    @POST("account")
    fun getAccountNumberWithPeriods(
        @Body body: AccountReceiptRequestBody
    ): Single<BaseResponse<List<AccountReceipt?>?>>

    @GET("contract/account")
    fun getAccountNumber(
        @Query("contract_guid") guid: String?
    ): Single<BaseResponse<AccountReceipt?>>

    @GET("options")
    fun getBankPayments(
        @Query("account_number") accountNumber: String?
    ): Single<BaseResponse<List<Bank?>?>>

    @POST("incorrect-data")
    fun reportBug(@Body requestBody: ReportBugRequestBody): Single<BaseResponse<Status?>>

    @GET("$API_V3_PATH/medicine/service-list")
    fun getMedicalServicesList(@QueryMap filters: MutableMap<String, String>): Single<BaseResponse<List<MedicineService?>?>>

    @GET("$API_V3_PATH/medicine/service")
    fun getMedicalServiceDetail(@Query("service_guid") serviceGuid: String?): Single<BaseResponse<MedicineServiceDetail?>>

    @GET("$API_V3_PATH/medicine/service/attachment")
    fun getMedicineServiceAttachment(@Query("service_guid") serviceGuid: String?): Single<BaseResponse<Attachment?>>

    @POST("$API_V3_PATH/medicine/service/upload-file")
    fun uploadMedicineFiles(@Body sendFilesInfo: SendFilesInfo?): Single<BaseResponse<Answer?>>

    @GET("$API_V3_PATH/medicine/service/application-list")
    fun getMedicalApplication(@Query("application_guid") application_guid: String?): Single<BaseResponse<ApplicationDetail?>>

    @GET("$API_V3_PATH/medicine/return")
    fun getMedicalReturn(): Single<BaseResponse<MedicalReturn?>>

    @GET("$API_V3_PATH/medicine/service/decline")
    fun getDeclinedDocs(@Query("service_guid") serviceGuid: String?): Single<BaseResponse<List<DeclinedDocuments?>?>>

    @POST("$API_V3_PATH/medicine/service/upload-file")
    fun uploadMedicineFilesDeclined(@Body declinedFilesSend: DeclinedFilesSend?): Single<BaseResponse<Answer?>>

    @POST("$API_V3_PATH/medicine/service/update-record")
    fun updateRecord(@Body createRecordRequestBody: CreateRecordRequestBody): Single<BaseResponse<RecordResponse?>?>

    @GET("$API_V3_PATH/medicine/service/cancel-record")
    fun cancelRecord(@Query("application_guid") applicationGuid: String?): Completable

    @GET("$API_V3_PATH/medicine/service/account")
    fun getMedicineServiceAccount(@Query("accountNum") accountNum: String?): Single<BaseResponse<Invoice?>?>

    @GET("$API_V3_PATH/medicine/service/account-qr")
    fun getMedicineServiceAccountFile(@Query("accountNum") accountNum: String?): Single<BaseResponse<ResponseString?>?>

    @GET("$API_V3_PATH/medicine/service/account-qr")
    fun getMedicineServiceAccountQr(@Query("accountNum") accountNum: String?): Single<BaseResponse<ResponseString?>?>

    @POST("$API_V3_PATH/medicine/service/account-pay")
    fun getMedicineServiceAccountPay(@Body payment: lc.deck.rudn.entity.medicine.payment.Payment?): Single<BaseResponse<List<ResponseCard?>?>>

    @GET("$API_V3_PATH/medicine/service/decision")
    fun getMedicalServiceDecisions(): Single<BaseResponse<Decision?>>

    @GET("${API_V3_PATH}/medicine/service/time-option")
    fun getMedicineTimeOptions(
        @Query("service_guid") serviceGuid: String?
    ): Single<BaseResponse<List<TimeOption?>>>

    @POST("${API_V3_PATH}/medicine/service/create-record")
    fun createRecord(
        @Body body: CreateRecordRequestBody
    ): Completable

    @GET("$API_V3_PATH/file")
    fun getHrTransactionFile(@Query("file_guid") fileGuid: String?): Single<BaseResponse<lc.deck.rudn.entity.personnel_transactions.File?>>

    @GET("employment/$API_V3_PATH/document")
    fun getDocumentsGroup(
        @QueryMap filters: MutableMap<String, String>
    ): Single<BaseResponse<List<HrTransaction>?>>

    @GET("$API_V3_PATH/personnel-certificate")
    fun getPersonnelCertificate(): Completable

    @POST("employment/v2/signature/initiate")
    fun initiateSignature(@Body signDocumentRequestBody: SignDocumentRequestBody): Single<BaseResponse<SignatureAnswer?>?>

    @POST("employment/$API_V2_PATH/documents-confirmation")
    fun getDocumentsConfirmation(@Body documentConfirmation: DocumentConfirmation): Completable

    @POST("$API_V3_PATH/signature/verify")
    fun signatureVerification(@Body smsCodeVerification: SmsCodeVerification): Completable

    @GET("$API_V3_PATH/application")
    fun getApplicationData(): Single<BaseResponse<ApplicationData?>?>

    @POST("$API_V3_PATH/application")
    fun sendApplication(@Body currentFilter: ApplicationData): Completable

    @GET("$API_V3_PATH/personnel-certificate/resend-pin")
    fun resendPin(): Completable

    @POST("$API_V3_PATH/personnel-certificate/verify")
    fun personnelCertificateVerify(@Body smsCodePin: SmsCodePin): Completable

    @GET("$API_V3_PATH/statement/period")
    fun getAssessmentSheetPeriods(): Single<BaseResponse<List<Period?>?>>

    @GET("$API_V3_PATH/statement/status-list")
    fun getStatementStatuses(): Single<BaseResponse<List<StatementStatus?>?>>

    @GET("$API_V3_PATH/statement/list")
    fun getStatementList(
        @QueryMap filters: MutableMap<String, String> = mutableMapOf()
    ): Single<BaseResponse<List<Statement?>?>>

    @GET("$API_V3_PATH/statement/{statement_id}")
    fun fetchStatementDetails(
        @Path("statement_id") statementId: Int?,
        @QueryMap filters: MutableMap<String, String> = mutableMapOf()
    ): Single<BaseResponse<Statement?>>

    @GET("$API_V3_PATH/statement/student-state-list")
    fun getStudentStatuses(): Single<BaseResponse<List<StatementStatus?>?>>

    @GET("$API_V3_PATH/statement/student-view/{statement_student_id}")
    fun getStudentDetails(
        @Path("statement_student_id") statementStudentId: Int?
    ): Single<BaseResponse<List<UndergraduateAcademGroup?>?>>

    @PATCH("$API_V3_PATH/statement-student/{statement_student_id}")
    fun setStudentResults(
        @Path("statement_student_id") statementStudentId: Int?,
        @Body body: StatementStudent?
    ): Single<BaseResponse<List<UndergraduateAcademGroup?>>>

    @POST("$API_V3_PATH/statement/send/{id}")
    fun sendResults(@Path("id") id: Int?): Completable

    @GET("$API_V3_PATH/agreement")
    fun getSignatureAgreement(): Single<BaseResponse<AgreementData?>?>

    @GET("$API_V3_PATH/library")
    fun getLibraryLink(): Single<BaseResponseSerial<Library?>>

    @GET("$API_V3_PATH/tech-support/services")
    fun getSupportServices(): Single<BaseResponse<List<SupportService?>?>?>

    @GET("$API_V3_PATH/tech-support/phone")
    fun getPhone(): Single<BaseResponse<Phone?>?>

    @GET("$API_V3_PATH/tech-support/reason")
    fun getSupportReason(@Query("id") id: String): Single<BaseResponse<List<Reason>?>?>

    @GET("$API_V3_PATH/tech-support/auditorium")
    fun getAuditorium(@Query("id") id: String?): Single<BaseResponse<Auditorium?>?>

    @POST("$API_V3_PATH/tech-support/appeal")
    fun sendAppeal(@Body appealBody: AppealBody): Single<BaseResponse<AppealResponse?>?>

    @POST("$API_V3_PATH/tech-support/complete")
    fun sendRating(@Body body: EstimateRequestBody?): Completable

    @POST("$API_V3_PATH/tech-support/return")
    fun returnToWork(@Body body: ReturnToWorkRequestBody?): Completable

    @GET("support/$API_PATH/appeal")
    fun getAppealList(): Single<BaseResponseSerial<List<AppealListItem>?>?>

    @GET("support/$API_PATH/appeal/{appealGUID}")
    fun getAppeal(
        @Path("appealGUID") appealGUID: String?
    ): Single<BaseResponseSerial<AppealItem>?>

    @GET("$API_V3_PATH/statement/{id}/statement-status-flow")
    fun fetchStatementStatuses(@Path("id") statementId: Int?): Single<BaseResponse<List<StatementStatusItem?>?>>

    @POST("$API_V3_PATH/statement/reject")
    fun sendStatementReject(@Query("id") statementId: Int?, @Body body: CommentRefuse): Completable

    @POST("$API_V3_PATH/correction/{id}/form")
    fun sendStatementCorrection(
        @Path("id") correctionId: Int?
    ): Completable

    @GET("$API_V3_PATH/correction")
    fun fetchCorrectionsList(
        @Query("filter[correction_state_id]") correctionStateId: Int?,
        @Query("filter[search]") s: String
    ): Single<BaseResponse<List<Correction?>?>>

    @GET("$API_V3_PATH/statement-reject")
    fun fetchRejectionsRequests(
        @Query("filter[statement_reject_status_id][]") correctionStatesId: List<Int?>?,
        @Query("filter[search]") s: String
    ): Single<BaseResponse<List<Correction?>?>>

    @GET("$API_V3_PATH/correction/{id}")
    fun fetchCorrectionDetail(
        @Path("id") correctionId: Int?,
    ): Single<BaseResponse<Correction?>?>

    @POST("$API_V3_PATH/correction/{id}/send")
    fun sendCorrectionApprove(
        @Path("id") correctionId: Int?,
    ): Completable

    @POST("$API_V3_PATH/correction-student/{id}/approve")
    fun approveOrDeclineCorrection(
        @Path("id") correctionStudentId: Int?,
        @Body approve: Approve,
    ): Single<BaseResponse<Correction>?>

    @POST("$API_V3_PATH/correction")
    fun postCorrection(
        @Body statementId: StatementId?,
    ): Completable

    @GET("$API_V3_PATH/correction/{id}/correction-student")
    fun getCorrectionStudents(
        @Path("id") correctionId: Int?,
    ): Single<BaseResponse<List<CorrectionStudents>>?>

    @POST("$API_V3_PATH/correction-student")
    fun setStudentCorrections(
        @Body studentCorrections: StudentCorrections,
    ): Completable

    @PUT("$API_V3_PATH/correction-student/{id}")
    fun updateStudentCorrections(
        @Path("id") studentId: Int?,
        @Body studentCorrections: StudentCorrections,
    ): Completable

    @DELETE("$API_V3_PATH/correction/{id}")
    fun cancelCorrection(
        @Path("id") correctionId: Int?,
    ): Completable

    @DELETE("$API_V3_PATH/correction-student/{id}")
    fun cancelCorrectionStudent(
        @Path("id") correctionId: Int?,
    ): Completable

    @POST("${API_V2_PATH}/service-order-file/{id}/signature")
    fun signOrCancelDocument(@Path("id") id: Int, @Body signature: Signature): Completable

    @GET("${API_V3_PATH}/service-order/{serviceOrderId}/cancel")
    fun cancelMfcApplication(@Path("serviceOrderId") id: Int): Completable

    @GET("$API_V3_PATH/family-status")
    fun getFamilyStatuses(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/vacancies")
    fun getVacancyLink(
    ): Single<BaseResponse<Vacancy?>>

    @GET("$API_V3_PATH/statement-reject/{id}")
    fun fetchRejectionDetail(
        @Path("id") rejectionId: Int?,
    ): Single<BaseResponse<Correction?>?>

    @POST("$API_V3_PATH/statement-reject/{id}/return")
    fun returnWithoutChanges(@Path("id") rejectionId: Int?): Single<BaseResponse<Correction?>>

    @POST("$API_V3_PATH/statement-reject/{id}/send")
    fun sendRejectionChanges(@Path("id") rejectionId: Int?): Single<BaseResponse<Correction?>>

    @POST("$API_V3_PATH/statement-reject/{id}/reset")
    fun cancelAllChanges(@Path("id") rejectionId: Int?): Single<BaseResponse<Correction?>>

    @GET("$API_V3_PATH/teacher")
    fun getTeacherList(
        @Query("filter[search]") searchQuery: String? = null,
        @Query("filter[department_id][]") departmentIds: List<Int?>? = null
    ): Single<BaseResponse<List<lc.deck.rudn.entity.assessment_sheets.Person?>>>

    @GET("$API_V3_PATH/department")
    fun getDepartments(
        @Query("filter[name]") currentQuery: String?
    ): Single<BaseResponse<List<Department?>>>

    @PATCH("$API_V3_PATH/statement-reject/{id}")
    fun saveTeachersData(
        @Path("id") statementRejectId: Int?,
        @Body teachersData: TeacherListRequestBody
    ): Completable

    @GET("$API_V3_PATH/statement-file/{id}/download")
    fun fetchStatementFile(
        @Path("id") id: Int?,
    ): Single<ResponseBody>

    @GET("$API_V3_PATH/statement/absence-reason")
    fun fetchAbsenceReasonsList(): Single<BaseResponse<AbsenceReasons?>>

    @GET("$API_V3_PATH/person/generate-pass-ble")
    fun generateBlePass(): Single<BaseResponse<Pass?>>

    @GET("$API_V3_PATH/service-order-document/{document_id}/download")
    fun downloadDocument(
        @Path("document_id") documentId: Int?,
    ): Single<ResponseBody?>

    @GET("$API_V3_PATH/discount-reason")
    fun fetchDiscountReasons(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/payment-option")
    fun fetchInstallmentPlan(
        @Query("payment_date") paymentDate: String,
        @Query("payment_sum") paymentSum: Number
    ): Single<BaseResponse<List<Installment?>?>>

    @GET("$API_V3_PATH/education-programm-type")
    fun fetchEducationProgramTypes(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/education-programm-benefits")
    fun fetchEducationProgramBenefits(@Query("filter[education_programm_type_id]") educationProgrammTypeId: Int): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/service")
    fun searchMfcServices(
        @Query("name") currentQuery: String?
    ): Single<BaseResponse<List<MfcSearchResult?>>>

    @GET("$API_V3_PATH/reason-call")
    fun fetchReasonCalls(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("$API_V3_PATH/applicant-category")
    fun fetchApplicantCategory(): Single<BaseResponse<List<SingleSelectionItem>>>

    @GET("payment/range")
    fun getDatesRange(@Query("contract_uuid") contractUUId: String): Single<BaseResponse<DateRange?>>

    @GET("payment/contract")
    fun getFilterPaymentContract(@Query("filter[guid]") contractUUId: String): Single<BaseResponse<List<PaymentContracts>>>

    @GET("$API_V3_PATH/policy")
    fun fetchPrivacyPolicy(): Single<BaseResponse<lc.deck.rudn.entity.personnel_transactions.PrivacyPolicyDoc?>?>

    @GET("$API_V3_PATH/personnel-instruction")
    fun fetchInstruction(): Single<BaseResponse<Instruction?>?>

    @GET("$API_V3_PATH/payment_dates")
    fun getPaymentDates(
        @Query("semester_type") semesterType: Int,
        @Query("first_payment") firstPayment: String? = null,
        @Query("second_payment") secondPayment: String? = null,
        @Query("third_payment") thirdPayment: String? = null,
        @Query("fourth_payment") fourthPayment: String? = null,
        @Query("fifth_payment") fifthPayment: String? = null,
        @Query("payments_num") paymentsNum: Int? = null,
    ): Single<BaseResponse<List<PaymentDate?>?>>


    @GET("payment/contract")
    fun getPaymentContract(@Query("filter[guid]") contractUuid: String):
            Single<BaseResponse<List<lc.deck.rudn.entity.multifunctional_center.PaymentContract?>?>>

    @GET("payment/range")
    fun fetchPaymentRange(@Query("contract_uuid") contractUuid: String):
            Single<BaseResponse<PaymentRange?>?>

    @GET("$API_V3_PATH/military-document-type")
    fun fetchMilitaryDocumentType(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/stock-categories")
    fun fetchMilitaryStockCategory(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/military-personnel")
    fun fetchMilitaryPersonnel(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/military-rank")
    fun fetchMilitaryRank(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/category-fitness")
    fun fetchMilitaryCategoryFitness(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/military-office")
    fun fetchMilitaryEnlistmentOffice(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/attitudes-military")
    fun fetchMilitaryAttitude(): Single<BaseResponse<List<SingleSelectionItem>?>>


    @GET("$API_V3_PATH/person/job")
    fun fetchPersonJobs(): Single<BaseResponse<List<PersonJob?>?>?>

    @GET("$API_V3_PATH/settlement-sheet-date")
    fun fetchLastAvailableDate(@Query("tab_num") tabNum: String): Single<BaseResponse<LastDate?>?>

    @GET("$API_PATH/settlement-sheet")
    fun fetchPayslipFile(
        @Query("date_calc") dateCalc: String?,
        @Query("tab_num") tabNum: String?,
        @Query("guid_employee") guidEmployee: String?,
    ): Single<ResponseBody?>

    @GET("$API_V3_PATH/settlement-sheet-faq")
    fun getFaq(): Single<BaseResponse<List<Faq?>?>?>

    @GET("$API_PATH/endowment-fund/description")
    fun getEndowmentFundDescription(): Single<BaseResponse<EndowmentDescription?>?>

    @GET("$API_PATH/endowment-fund/link")
    fun getEndowmentFundLinks(): Single<BaseResponse<List<EndowmentFundLink?>?>?>

    @GET("$API_PATH/endowment-fund/contact")
    fun getEndowmentFundContacts(): Single<BaseResponse<List<EndowmentFundContact?>?>?>

    @GET("$API_PATH/endowment-fund/capital")
    fun getEndowmentFundCapital(@Query("filter[name]") name: String? = null):
            Single<BaseResponse<List<EndowmentFundCapital?>?>?>

    @GET("$API_PATH/endowment-fund/capital-payment-type/{id}")
    fun fetchCapitalPaymentType(
        @Path("id") capitalId: Int?,
    ): Single<BaseResponse<CapitalPaymentType?>?>

    @GET("$API_PATH/endowment-fund/bank")
    fun fetchPaymentBank(): Single<BaseResponse<List<PaymentBank>?>?>

    @GET("$API_PATH/endowment-fund/capital-payment-detail/{id}")
    fun fetchCapitalPaymentDetail(
        @Path("id") capitalId: Int?,
    ): Single<BaseResponse<CapitalPaymentDetail?>?>

    @GET("$API_PATH/endowment-fund/capital-file/{id}")
    fun fetchCapitalPaymentFile(
        @Path("id") id: Int?,
    ): Single<ResponseBody?>

    @GET("$API_PATH/endowment-fund/external-link")
    fun fetchEndowmentFundRudnLink(): Single<BaseResponse<EndowmentFundLinkWeb?>?>


    @GET("enrollee/v2.3/application")
    fun getApplicationsList(): Single<BaseResponse<List<lc.deck.rudn.entity.enrollee.application.Application?>?>>

    @GET("enrollee/v2.3/application/{guid}")
    fun getApplication(@Path("guid") guid: String): Single<BaseResponse<lc.deck.rudn.entity.enrollee.application.Application?>>

    @GET("enrollee/contract")
    fun getContract(@QueryMap filters: Map<String?, String?> = mapOf()): Single<BaseResponse<List<Contract?>?>>

    @GET("enrollee/payment/type")
    fun getPaymentType(@Query("contract_guid") contractGuid: String?): Single<BaseResponse<PaymentSchedule?>>

    @GET("enrollee/payment/plan")
    fun getPaymentPlan(
        @Query("contract_guid") contractGuid: String?,
        @Query("payment_type_guid") paymentTypeGuid: String?
    ): Single<BaseResponse<PaymentPlan?>>

    @POST("enrollee/v2.0/payment-type/select")
    fun getFormedContract(@Body body: ContractPaymentTypeGuid): Completable

    @GET("enrollee/account")
    fun getContractAccount(
        @Query("contract_guid") contractGuid: String,
        @Query("payment_date") paymentDate: String
    ): Single<BaseResponse<ContractAccount?>>

    @GET("enrollee/account/options")
    fun getBanks(@Query("account_number") accountNumber: String?): Single<BaseResponse<List<lc.deck.rudn.entity.payment.bank.Bank?>?>>

    @GET("enrollee/account/qr")
    fun fetchQrForPayment(@Query("account_number") accountNumber: String?): Single<BaseResponse<QrLink?>>

    @GET("enrollee/account/print")
    fun fetchRequisitesLink(@Query("account_number") accountNumber: String?): Single<BaseResponse<QrLink?>>

    @GET("enrollee/contract/document")
    fun fetchContractFileInfo(
        @Query("contract_guid") contractGuid: String?,
        @Query("document_guid") documentGuid: String?
    ): Single<BaseResponse<FileLink?>>

    @POST("enrollee/v2.0/contract/sign")
    fun requestContractSign(@Body body: FileGuid): Single<BaseResponse<ContractSign?>>

    @POST("enrollee/contract/sign-code")
    fun contractSignCodeVerification(
        @Body body: CodeVerification
    ): Completable

    @POST("enrollee/personal-data/sign")
    fun personalDataSign(): Single<BaseResponse<ContractSign?>>

    @POST("enrollee/personal-data/sign-code")
    fun personalDataSignCodeVerification(
        @Body body: CodeVerification
    ): Completable

    @GET("enrollee/v2.1/personal-data")
    fun getPersonalData(): Single<BaseResponse<EnrolleeData?>>

    @GET("enrollee/personal-data/support-description")
    fun getSupportDescription(): Single<BaseResponse<SupportDescription?>>

    @GET("$API_V3_PATH/person/phones")
    fun getPhones(): Single<BaseResponse<List<PhoneNumbers?>?>>

    @GET("enrollee/personal-data/status-text")
    fun fetchPersonalDataStatusText(): Single<BaseResponse<PersonalDataStatusText?>>

    @GET("$API_V3_PATH/person/mfc-description")
    fun getMfcDescription(): Single<BaseResponse<AbsentPhotoDescription?>?>

    @GET("enrollee/v1/translator-edu-parameters/")
    fun fetchEduParameters(): Single<BaseResponse<List<EduDivision?>?>>

    @POST("enrollee/v2.0/translator-contract-create")
    fun setTranslatorContractCreate(
        @Body translatorContractCreate: TranslatorContractCreate,
    ): Single<BaseResponse<SubmitAnswer?>>

    @GET("$API_PATH/privacy-policy")
    fun getPrivacyPolicyLink(): Single<BaseResponse<PrivacyPolicy?>?>

    @GET("$API_V3_PATH/citizenship")
    fun fetchCitizenship(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_V3_PATH/education-level")
    fun fetchEduLevel(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/v1.0/identity-card")
    fun fetchIdentityCard(@Query("filter[name]") searchQuery: String?): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("vacation/v1/leftovers")
    fun getVacationLeftover(@Query("tableNumber") tableNumber: String): Single<BaseResponse<VacationLeftover>>

    @GET("$API_PATH/translate-resource")
    fun getResources(@Query("app_id") appId: Int): Single<BaseResponse<Resources?>?>

    @GET("mfc/$API_PATH/position")
    fun fetchWorkignPositions(@Query("person_id") personId: Int?): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("$API_PATH/mfc/payment")
    fun fetchPayment(
        @Query("person_id") personId: Int?,
        @Query("contract_id") contractId: Int?,
        @Query("start_date") startDate: String?,
        @Query("end_date") endDate: String?
    ): Single<BaseResponse<List<PaymentOrder>?>>

    @GET("$API_PATH/parking-payment/bill-payment")
    fun fetchQrOrPdfForUnpaidBills(
        @Query("invoice_number") invoiceNumber: String,
        @Query("type") type: Int
    ): Single<ResponseBody?>

    @GET("$API_PATH/parking-payment/contract")
    fun fetchContracts(
        @Query("person_id") personId: String,
        @Query("filter[date_from]") dateFrom: String?,
        @Query("date_to") dateTo: String?,
    ): Single<BaseResponse<Contracts?>?>

    @GET("$API_PATH/parking-payment/offer-list")
    fun fetchOffers(
        @Query("person_id") personId: String,
        @Query("filter[offer_number]") offerNumber: String? = null
    ): Single<BaseResponse<List<lc.deck.rudn.entity.contract_and_payment.offer.Offer?>?>?>

    @GET("$API_PATH/parking-payment/offer-doc")
    fun fetchOfferFile(@Query("offer_number") offerNumber: String?): Single<ResponseBody?>

    @GET("$API_PATH/parking-payment/bill")
    fun fetchBills(
        @Query("person_id") personId: String,
        @Query("filter[contract_number]") contractNumber: String? = null,
        @Query("filter[date_from]") dateFrom: String?,
        @Query("filter[date_to]") dateTo: String?,
    ): Single<BaseResponse<UnpaidBills?>?>

    @GET("$API_PATH/parking-payment/fio-email")
    fun fetchPaymentFullName(@Query("person_id") personId: String): Single<BaseResponse<PayerInfo?>?>

    @POST("$API_PATH/parking-payment/issue-invoice")
    fun issueParkingInvoice(@Body infoForInvoice: InfoForInvoice): Completable

    @GET("payment/acquiring")
    fun getPaymentAcquiringLink(
        @Query("accountnum") accountNumber: String?,
        @Query("email") email: String?
    ): Single<BaseResponse<PaymentAcquiringLink?>?>

    @GET("$API_PATH/config")
    fun getConfigs(@Query("key") key: String): Single<BaseResponseSerial<lc.deck.rudn.entity.config.Config?>?>

    @GET("fhd/$API_PATH/person-access")
    fun getCfoCabinetAvailability(@Query("person_id") personId: Int?): Completable

    @GET("fhd/$API_PATH/report/find-by-person")
    fun getAvailableCfoReports(
        @Query("person_id") personId: Int?,
        @Query("filter[name]") reportName: String?,
    ): Single<BaseResponse<List<AvailableReport?>?>?>

    @GET("fhd/$API_PATH/report-task/find-by-person")
    fun getFormedCfoReports(
        @Query("person_id") personId: Int?,
        @Query("filter[name]") reportName: String?,
    ): Single<BaseResponse<List<FormedReports?>?>?>

    @GET("fhd/$API_PATH/report-task/get-file")
    fun fetchReportFile(
        @Query("report_task_id") reportTaskId: String?
    ): Single<ResponseBody?>

    @POST("fhd/$API_PATH/report-task")
    fun formCfoReport(
        @Body formReport: FormReport
    ): Single<BaseResponse<ReportSend?>>

    @GET("fhd/$API_PATH/report/find-by-id")
    fun fetchReportDepartmentsNames(
        @Query("person_id") personId: Int?,
        @Query("report_id") reportId: String?,
        @Query("filter[cfo]") cfoName: String?,
    ): Single<BaseResponse<ReportDepartments?>?>


    @GET("$API_PATH/staff")
    fun getStaff(@Query("person_id") personId: String?): Single<BaseResponseSerial<List<PersonalDataItem?>?>?>

    @GET("$API_PATH/phonebook/contact")
    fun getPhonebookContact(
        @Query("filter[search]") search: String?
    ): Single<BaseResponseSerial<List<PhonebookContact>?>>

    @GET("dorm/v1/application")
    fun getDormitoryApplications(@Query("person_id") personId: Int?):
            Single<BaseResponseSerial<List<DormitoryApplication?>?>>

    @GET("dorm/$API_PATH/family-status")
    fun getDormApplicationsFamilyStatuses(@Query("person_id") personId: Int?):
            Single<BaseResponseSerial<List<lc.deck.rudn.entity.dormitory.family_status.FamilyStatus?>?>>

    @GET("dorm/$API_PATH/config")
    fun getDormApplicationsConfig(): Single<BaseResponseSerial<Config?>>

    @POST("dorm/$API_PATH/apply")
    fun applyForDormitory(@Body dormitoryApplyForm: DormitoryApplyForm): Completable

    @GET("dorm/$API_PATH/steps")
    fun getDormSteps(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<DormitorySteps?>?>

    @GET("dorm/$API_PATH/application-info")
    fun getDormApplicationInfo(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<ApplicationInfo?>?>

    @GET("dorm/$API_PATH/rejection")
    fun getDormRejection(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<Rejection?>?>

    @GET("dorm/$API_PATH/link")
    fun getDormLink(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<DormLink?>?>

    @GET("dorm/$API_PATH/arrival")
    fun getDormInfoForCheckIn(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<CheckInInfo?>?>

    @GET("dorm/$API_PATH/time-options")
    fun getDormCheckInTimeOptions(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<CheckInTimeInfo?>?>

    @POST("dorm/$API_PATH/arrival-create")
    fun setDormCheckInTime(
        @Body checkInTimeSet: CheckInTimeSet
    ): Single<BaseResponseSerial<CheckInInfo?>?>

    @PATCH("dorm/$API_PATH/arrival-update")
    fun updateDormCheckInTime(
        @Body checkInTimeSet: CheckInTimeSet
    ): Single<BaseResponseSerial<CheckInInfo?>?>

    @POST("dorm/$API_PATH/arrival-cancel")
    fun cancelDormCheckInSignedUpTime(
        @Body checkInCancel: CheckInCancel
    ): Completable

    @GET("dorm/$API_PATH/contract")
    fun getDormContract(
        @Query("application_guid") applicationGuid: String?,
        @Query("person_id") personId: Int?,
        @Query("contract_type_id") contractTypeId: Int
    ): Single<BaseResponseSerial<DormitoryContract?>?>

    @GET("dorm/$API_PATH/contract/document")
    fun downloadDormitoryContract(
        @Query("contract_guid") contractGuid: String?,
        @Query("document_guid") documentGuid: String?
    ): Single<BaseResponseSerial<DownloadFile?>?>

    @GET("dorm/$API_PATH/contract/payment-plan")
    fun getDormContractPaymentPlan(
        @Query("application_guid") applicationGuid: String?,
    ): Single<BaseResponseSerial<lc.deck.rudn.entity.dormitory.payment_plan.PaymentPlan?>?>

    @GET("dorm/$API_PATH/personal-data")
    fun getDormPersonalData(
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<DormPersonalData?>?>

    @POST("dorm/$API_PATH/personal-data/sign")
    fun dormPersonalDataSign(
        @Body body: PhoneNumber
    ): Single<BaseResponse<ContractSign?>>

    @POST("dorm/$API_PATH/personal-data/sign-code")
    fun dormPersonalDataSignCodeVerification(
        @Body body: CodeVerification
    ): Completable

    @GET("dorm/$API_PATH/account")
    fun getDormContractAccount(
        @Query("person_id") personId: Int?,
        @Query("contract_guid") contractGuid: String,
    ): Single<BaseResponseSerial<lc.deck.rudn.entity.dormitory.contract.ContractAccount?>>

    @GET("dorm/$API_PATH/payment/qr")
    fun fetchDormQrForPayment(
        @Query("account_number") accountNumber: String?
    ): Single<ResponseBody?>

    @GET("dorm/$API_PATH/payment/print")
    fun fetchDormRequisitesLink(
        @Query("account_number") accountNumber: String?
    ): Single<BaseResponseSerial<lc.deck.rudn.entity.dormitory.payment.QrLink?>>

    @POST("dorm/$API_PATH/contract/sign-code")
    fun contractDormSignCodeVerification(
        @Body contractVerification: ContractVerification
    ): Completable

    @POST("dorm/$API_PATH/contract/sign")
    fun requestDormContractSign(@Body body: ContractFiles): Single<BaseResponse<ContractSign?>>

    @GET("document/$API_PATH/tree")
    fun getDocumentTree(): Single<BaseResponseSerial<List<DocumentTree?>?>?>

    @GET("document/$API_PATH/content/{id}")
    fun getDocumentContent(@Path("id") id: Int): Single<BaseResponseSerial<DocumentContent?>?>

    @GET("vacation/$API_PATH/vacation-schedule/user")
    fun getVacationScheduleUser(
        @Query("filter[year]") year: String?,
        @Query("filter[state]") state: String?,
        @Query("filter[typeGUID]") typeGuid: String?,
        @Query("filter[tabNumber]") tabNumber: String?
    ): Single<BaseResponseSerial<List<VacationScheduleUser>?>>

    @GET("vacation/$API_PATH/balance")
    fun getVacationScheduleBalance(): Single<BaseResponseSerial<List<Balance>?>>

    @GET("vacation/$API_PATH/vacation-status")
    fun getVacationScheduleStatus(): Single<BaseResponseSerial<List<VacationStatus>?>>

    @GET("med/$API_PATH/medical-examination")
    fun getDormMedicalExamination(): Single<BaseResponseSerial<MedicalExamination?>>

    @Multipart
    @POST("med/$API_PATH/file/upload")
    fun fileUpload(
        @PartMap examinationResult: HashMap<String, RequestBody>
    ): Single<BaseResponseSerial<MedicalExamination?>>

    @POST("med/$API_PATH/file/delete")
    fun fileDelete(
        @Query("fileID") fileId: String?
    ): Single<BaseResponseSerial<MedicalExamination?>>

    @POST("med/$API_PATH/file/download")
    fun fileDownload(
        @Query("fileID") fileId: String?
    ): Single<ResponseBody?>

    @POST("med/$API_PATH/medical-examination/send")
    fun getDormMedicalExaminationSend(
        @Body body: MedicalExaminationSend
    ): Single<BaseResponseSerial<MedicalExamination?>>

    @POST("med/$API_PATH/personal-data/sign")
    fun dormMedicalPersonalDataSign(
        @Query("phone") phone: String?
    ): Single<BaseResponse<ContractSign?>>

    @POST("med/$API_PATH/personal-data/sign-code")
    fun medPersonalDataSignCodeVerification(
        @Query("code") code: String?,
        @Query("phone") phone: String?
    ): Completable

    @GET("dorm/$API_PATH/types")
    fun getDormTypes(
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<List<TypeDormApplication>?>>

    @GET("med/$API_PATH/registration/dates")
    fun getMedicalRegistrationDates(): Single<BaseResponseSerial<List<RegistrationDates>?>>

    @GET("med/$API_PATH/registration/index")
    fun dormMedicalRegistrationIndex(
        @Query("conclusionID") conclusionID: String?,
        @Query("date") date: String?
    ): Single<BaseResponseSerial<MedicalExamination?>>

    @GET("qr-scan/$API_V1_0_PATH/is-it-start")
    fun getIsLessonStarted(@Query("auditorium_guid") auditoriumNumber: String): Single<BaseResponse<LessonStarted?>?>

    @POST("qr-scan/$API_V1_0_PATH/attend-start")
    fun setLessonBegin(@Body auditoriumNumber: AuditoriumNumber): Single<BaseResponse<LessonBeginEnd?>?>

    @POST("qr-scan/$API_V1_0_PATH/attend-end")
    fun setLessonEnd(@Body auditoriumNumber: AuditoriumNumber): Single<BaseResponse<LessonBeginEnd?>?>

    @GET("qr-scan/$API_V1_0_PATH/schedule")
    fun getAuditoriumSchedule(
        @Query("auditorium_guid") auditoriumNumber: String,
        @Query("date") date: String?,
    ): Single<BaseResponse<Schedule?>?>

    @GET("mdk/$API_PATH/thematic")
    fun fetchMdkThemes(): Single<BaseResponse<List<MdkTheme?>?>?>

    @GET("mdk/$API_PATH/faculty")
    fun fetchMdkInstitutesAndFaculties(): Single<BaseResponse<List<MdkFaculty?>?>?>

    @GET("mdk/$API_PATH/subject")
    fun fetchMdkSubjects(
        @Query("sort") sort: String? = null,
        @Query("filter[academ_group_id]") acacemGroupId: Int?,
        @Query("filter[study_period_id]") studyPeriodId: String?,
        @Query("filter[thematic_uuid][]") thematicUuids: List<String?>? = null,
        @Query("filter[faculty_id][]") facultyIds: List<String?>? = null,
        @Query("filter[name]") name: String? = null
    ): Single<BaseResponse<List<MdkSubject?>?>?>

    @GET("mdk/$API_PATH/study-period")
    fun getMdkStudyPeriod(): Single<BaseResponse<List<MdkFaculty>?>?>

    @GET("mdk/$API_PATH/academ-group")
    fun getMdkAcademGroup(): Single<BaseResponse<List<MdkFaculty>?>?>

    @GET("mdk/$API_PATH/subject/approved")
    fun fetchMdkApprovedSubjects(
        @Query("sort") sort: String? = null,
        @Query("filter[academ_group_id]") acacemGroupId: Int?,
        @Query("filter[study_period_id]") studyPeriodId: String?,
    ): Single<BaseResponse<List<MdkSubject?>?>?>

    @POST("mdk/$API_PATH/course/sign-up")
    fun courseSignUp(
        @Body body: CourseSignUp
    ): Completable

    @POST("mdk/$API_PATH/course/cancel")
    fun courseCancel(
        @Body body: CourseSignUp
    ): Completable

    @GET("vaccination/$API_PATH/vaccination")
    fun getVaccination(@Query("personID") personId: Int?): Single<BaseResponseSerial<Vaccination?>?>

    @GET("vaccination/$API_PATH/vaccines")
    fun getVaccines(): Single<BaseResponseSerial<List<ItemAddVaccineSingleSelector>?>?>

    @POST("vaccination/$API_PATH/vaccines/add-vaccine")
    fun vaccinesAddVaccine(@Body body: AddVaccine): Completable

    @GET("vaccination/$API_PATH/certificate")
    fun getCertificates(@Query("personID") personId: Int?): Single<BaseResponseSerial<List<Certificate>?>?>

    @GET("vaccination/$API_PATH/certificate/file")
    fun getCertificateFile(@Query("id") id: String?): Single<ResponseBody?>

    @GET("$API_PATH/app-map/search")
    fun getAppMapSearch(
        @Query("q") q: String?
    ): Single<BaseResponseSerial<List<AppMapSearch?>?>>

    @GET("$API_V3_PATH/diploma/file")
    fun getDiplomaFile(
        @Query("fileGUID") fileGuid: String?
    ): Single<ResponseBody?>

    @GET("$API_PATH/science")
    fun getScience(@Query("person_id") personId: String?): Single<BaseResponseSerial<List<Science?>?>?>

    @GET("$API_PATH/studies-and-jobs")
    fun getStudiesAndJobs(): Single<BaseResponseSerial<StudiesAndJobs?>?>

    @GET("sport/$API_PATH/playground")
    fun getPlayground(
        @Query("person_id") personId: Int?,
        @Query("sportType") sportType: String?,
    ): Single<BaseResponseSerial<List<Playground>?>>

    @GET("sport/$API_PATH/directory/sportType")
    fun getDirectorySportType(
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<List<DirectoryItem>?>>

    @GET("sport/$API_PATH/playground/{id}")
    fun getPlaygroundById(
        @Path("id") id: String?,
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<Playground>?>

    @GET("sport/$API_PATH/schedule")
    fun getSchedule(
        @Query("person_id") personId: Int?,
        @Query("subPlaygroundID") subPlaygroundID: String?,
        @Query("serviceID") serviceID: String?,
        @Query("start") start: String?,
        @Query("end") end: String?,
    ): Single<BaseResponseSerial<List<SportGroundSchedule>?>>

    @GET("sport/$API_PATH/service")
    fun getScheduleService(
        @Query("person_id") personId: Int?,
        @Query("subPlaygroundID") subPlaygroundID: String?
    ): Single<BaseResponseSerial<ScheduleServices?>>

    @POST("sport/$API_PATH/playground/rent")
    fun playgroundRent(
        @Query("personID") personId: Int?,
        @Body body: PlaygroundRentBody
    ): Single<BaseResponseSerial<PlaygroundRent?>>

    @GET("sport/$API_PATH/payment")
    fun getPayments(
        @Query("person_id") personId: Int?,
        @Query("paymentID") paymentID: String?
    ): Single<BaseResponseSerial<List<FokBankItem>?>>

    @GET("sport/$API_PATH/directory/direction")
    fun getDirectoryDirection(
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<List<DirectoryItem>?>>

    @GET("sport/$API_PATH/atonement/info")
    fun getAbonementInfo(
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<AbonementInfo?>>

    @GET("sport/$API_PATH/abonement")
    fun getAbonement(
        @Query("person_id") personId: Int?,
        @Query("direction") direction: String?
    ): Single<BaseResponseSerial<List<Abonement>?>>

    @GET("sport/$API_PATH/directory/section")
    fun getDirectorySection(
        @Query("person_id") personId: Int?,
        @Query("subscriptionID") subscriptionID: String?
    ): Single<BaseResponseSerial<List<DirectoryItem>?>>

    @GET("sport/$API_PATH/directory/instructor")
    fun getDirectoryInstructor(
        @Query("person_id") personId: Int?,
        @Query("subscriptionID") subscriptionID: String?
    ): Single<BaseResponseSerial<List<DirectoryItem>?>>

    @GET("sport/$API_PATH/directory/period")
    fun getDirectoryPeriod(
        @Query("person_id") personId: Int?,
        @Query("subscriptionID") subscriptionID: String?
    ): Single<BaseResponseSerial<List<DirectoryItem>?>>

    @GET("sport/$API_PATH/section")
    fun getSection(
        @Query("person_id") personId: Int?,
        @Query("abonementID") abonementID: String?,
        @Query("sectionID") sectionID: String?,
        @Query("instructorID") instructorID: String?,
        @Query("periodID") periodID: String?
    ): Single<BaseResponseSerial<List<Section>?>>

    @POST("sport/$API_PATH/abonement/rent")
    fun abonementRent(
        @Query("personID") personId: Int?,
        @Body body: AbonementRent
    ): Single<BaseResponseSerial<PlaygroundRent?>>

    @POST("sport/$API_PATH/abonement/sum")
    fun abonementSum(
        @Query("personID") personId: Int?,
        @Body body: AbonementRent
    ): Single<BaseResponseSerial<Sum?>>

    @POST("sport/$API_PATH/section/registration")
    fun sectionRegistration(
        @Query("personID") personId: Int?,
        @Body body: SectionRegistration
    ): Single<BaseResponseSerial<PlaygroundRent?>>

    @GET("sport/$API_PATH/ticket")
    fun getTickets(
        @Query("person_id") personId: Int?
    ): Single<BaseResponseSerial<List<Tickets>?>>

    @GET("journal/$API_PATH/journal-list")
    fun getJournalList(
        @Query("personID") personId: Int?,
        @Query("student") student: Int?,
        @Query("disciplineID") disciplineId: String?,
        @Query("periodID") periodId: String?,
        @Query("search") search: String?
    ): Single<BaseResponseSerial<List<Journal>?>>

    @GET("journal/$API_PATH/journal")
    fun getJournal(
        @Query("personID") personId: Int?,
        @Query("id") id: String?,
        @Query("eventID") eventId: String?,
        @Query("academGroupID") academGroupId: String?,
        @Query("student") student: Int?,
        @Query("periodStart") periodStart: String?,
        @Query("periodEnd") periodEnd: String?,
        @Query("search") search: String?
    ): Single<BaseResponseSerial<Journal?>>

    @POST("journal/$API_PATH/journal/update")
    fun updateJournal(
        @Query("personID") personId: Int?,
        @Body body: UpdateJournalBody
    ): Completable

    @GET("journal/$API_PATH/directory/discipline")
    fun getJournalDiscipline(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<JournalDirectoryItem>?>>

    @GET("journal/$API_PATH/directory/period")
    fun getJournalPeriod(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<JournalDirectoryItem>?>>

    @GET("journal/$API_PATH/directory/academ-groups")
    fun getJournalAcademGroups(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<JournalDirectoryItem>?>>

    @GET("journal/$API_PATH/directory/control-forms")
    fun getJournalControlForms(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<JournalDirectoryItem>?>>

    @GET("journal/$API_PATH/directory/theme")
    fun getJournalTheme(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<JournalDirectoryItem>?>>

    @POST("journal/$API_PATH/journal/update")
    fun createEvent(
        @Query("personID") personId: Int?,
        @Body body: CreateEventBody
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.journal.journal.Event>?>>

    @GET("iep/$API_PATH/iep")
    fun getIep(
        @Query("personID") personId: Int?,
        @Query("periodID") periodId: String?,
        @Query("programID") programId: String?,
    ): Single<BaseResponseSerial<IndividualEduPlan?>?>

    @POST("iep/$API_PATH/iep/approve")
    fun iepApprove(
        @Body body: IepAproveBody
    ): Single<BaseResponseSerial<IndividualEduPlan?>>

    @GET("iep/$API_PATH/disciplines")
    fun getDisciplines(
        @Query("personID") personId: Int?,
        @Query("periodID") periodId: String?,
        @Query("programID") programId: String?,
        @Query("search") search: String?
    ): Single<BaseResponseSerial<Discipline?>?>

    @POST("iep/$API_PATH/disciplines/sign")
    fun disciplinesSign(
        @Body body: DisciplinesSignBody
    ): Single<BaseResponseSerial<Discipline?>>

    @GET("iep/$API_PATH/disciplines/signs")
    fun getDisciplinesSigns(
        @Query("personID") personId: Int?,
        @Query("periodID") periodId: String?,
        @Query("programID") programId: String?,
        @Query("search") search: String?
    ): Single<BaseResponseSerial<Discipline?>?>

    @GET("iep/$API_PATH/directory/period")
    fun getDirectoryIepPeriod(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.iep.DirectoryItem>?>?>

    @GET("iep/$API_PATH/directory/discipline-period")
    fun getDirectoryDisciplinePeriod(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.iep.DirectoryItem>?>?>

    @GET("iep/$API_PATH/directory/educational-program")
    fun getDirectoryEducationalProgram(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.iep.DirectoryItem>?>?>

    @GET("mfc/$API_PATH/reason-shift-fio")
    fun fetchReasonShiftFio(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/achievement-type")
    fun fetchAchievementType(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/achievement-category")
    fun fetchAchievementCategory(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/achievement-level")
    fun fetchAchievementLevel(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/reason-transfer")
    fun fetchReasonTransfer(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/transfer-option")
    fun fetchTransferOption(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/semester")
    fun fetchSemester(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/academic-year")
    fun fetchAcademicYear(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/currency")
    fun fetchCurrency(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/education-type")
    fun fetchEducationType(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/driving-program")
    fun fetchDrivingProgram(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/driving-certificate-category")
    fun fetchDrivingCertificateCategory(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/faculty")
    fun fetchFaculty(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/finance-type")
    fun fetchFinanceType(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/education-level-extended")
    fun fetchEducationLevelExtended(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/payment-period")
    fun fetchPaymentPeriod(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/education-program-parent")
    fun fetchEducationProgramParent(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/reason-budget")
    fun fetchReasonBudget(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/archive-visit-reason")
    fun fetchArchiveVisitReason(): Single<BaseResponse<List<SingleSelectionItem>?>>

    @GET("mfc/$API_PATH/archive-gap-visit")
    fun fetchArchiveGapVisit(): Single<BaseResponse<List<MfcDate>>>

    @POST("$API_V3_PATH/file/download")
    fun diplomaFileDownload(
        @Query("fileID") fileId: String?,
        @Query("module") module: String?
    ): Single<ResponseBody?>

    @GET("$API_PATH/dpo/get-documents")
    fun getDiplomaAdditionalEducationDocuments(
        @Query("personID") personId: Int?
    ): Single<BaseResponseSerial<List<AdditionalEduDocument>?>?>

    @GET("$API_V3_PATH/reports/teaching-staff")
    fun getTeachingStaff(
        @Query("personID") personId: Int?,
        @Query("periodID") periodId: String?
    ): Single<BaseResponseSerial<List<TeachingStaff>?>?>

    @GET("$API_V3_PATH/directory/teaching-staff-periods")
    fun getTeachingStaffPeriod(
        @Query("personID") personId: Int?,
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.iep.DirectoryItem>?>?>

    @GET("$API_V3_PATH/reports/discipline-work-program")
    fun getDisciplineWorkProgram(
        @Query("personID") personId: Int?,
        @Query("periodID") periodId: String?,
        @Query("mainEducationalUnitID") mainEducationalUnitID: String?
    ): Single<BaseResponseSerial<List<DisciplineWorkProgram>?>?>

    @GET("$API_V3_PATH/directory/discipline-work-program-periods")
    fun getDisciplineWorkProgramPeriod(
        @Query("personID") personId: Int?,
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.iep.DirectoryItem>?>?>

    @GET("$API_V3_PATH/directory/main-educational-units")
    fun getMainEducationalUnits(
        @Query("personID") personId: Int?,
    ): Single<BaseResponseSerial<List<lc.deck.rudn.entity.iep.DirectoryItem>?>?>

    @GET("employment/$API_V3_PATH/directory/dismissal-reasons")
    fun getDismissalReasons(): Single<BaseResponse<List<StatementReason>>>

    @GET("employment/$API_V3_PATH/directory/vacation-reasons")
    fun getVacationReasons(): Single<BaseResponse<List<StatementReason>>>

    @GET("employment/$API_V3_PATH/directory/subdivisions")
    fun getSubdivisions(): Single<BaseResponse<List<StatementReason>>>

    @GET("employment/$API_V3_PATH/directory/positions")
    fun getPositions(@Query("subdivisionID") subdivisionId: String): Single<BaseResponse<List<StatementReason>>>

    @GET("employment/$API_V3_PATH/directory/rates")
    fun getRates(): Single<BaseResponse<List<Double>>>

    @POST("employment/$API_V3_PATH/dismissal-application")
    fun sendDismissal(@Body statement: DismissalStatementRequest): Single<BaseResponse<SendStatementResponse>>

    @POST("employment/$API_V3_PATH/permanent-remote-work-transfer-application")
    fun sendRemotePermanent(@Body statement: RemotePermanentStatementRequest): Single<BaseResponse<SendStatementResponse>>

    @POST("employment/$API_V3_PATH/temporary-remote-work-transfer-application")
    fun sendRemoteTemporary(@Body statement: RemoteTemporaryStatementRequest): Single<BaseResponse<SendStatementResponse>>

    @POST("employment/$API_V3_PATH/vacation-without-pay-application")
    fun sendLeave(@Body statement: LeaveStatementRequest): Single<BaseResponse<SendStatementResponse>>

    @POST("employment/$API_V3_PATH/transfer-application-with-structure")
    fun sendTransferPosition(@Body statement: TransferPositionStatementRequest): Single<BaseResponse<SendStatementResponse>>

    @POST("employment/$API_V3_PATH/bid-changing-application")
    fun sendRate(@Body statement: RateStatementRequest): Single<BaseResponse<SendStatementResponse>>

    @POST("employment/$API_V3_PATH/vacation-application")
    fun sendVacation(@Body statement: VacationStatementRequest): Single<BaseResponse<SendStatementResponse>>
}