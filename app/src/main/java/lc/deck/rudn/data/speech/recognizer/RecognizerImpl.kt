package lc.deck.rudn.data.speech.recognizer

import com.microsoft.cognitiveservices.speech.SpeechConfig
import com.microsoft.cognitiveservices.speech.SpeechRecognitionEventArgs
import com.microsoft.cognitiveservices.speech.SpeechRecognizer
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.microsoft.cognitiveservices.speech.util.EventHandler
import io.reactivex.Observable
import lc.deck.rudn.system.LocaleHolder
import javax.inject.Inject
import javax.inject.Named

class RecognizerImpl @Inject constructor(
    speechConfig: SpeechConfig,
    @Named("ru") speechConfigRu: SpeechConfig,
    private val localeHolder: LocaleHolder
) : Recognizer {

    private val microphoneStream = MicrophoneStream()
    private val audioInput = AudioConfig.fromStreamInput(microphoneStream)
    private val speechRecognizer = SpeechRecognizer(speechConfig, audioInput)
    private val speechRecognizerRu = SpeechRecognizer(speechConfigRu, audioInput)

    override fun recognizeSpeech() = Observable.create<RecognitionResult> { emitter ->

        val listener =
            EventHandler<SpeechRecognitionEventArgs> { _, speechRecognitionEventArgs ->
                speechRecognitionEventArgs?.result?.text?.let {
                    if (!emitter.isDisposed) emitter.onNext(RecognitionResult.TempText(it))
                }
            }

        val speechRecognizer =
            if (localeHolder.getLocale() == "ru-RU") speechRecognizerRu
            else speechRecognizer

        speechRecognizer.recognizing.addEventListener(listener)

        microphoneStream.dbListener = { volumeLevel ->
            if (!emitter.isDisposed) emitter.onNext(RecognitionResult.VolumeLevel(volumeLevel))
        }

        try {
            val result = speechRecognizer.recoImpl.Recognize()
            if (!emitter.isDisposed) {
                emitter.onNext(RecognitionResult.ResultText(result.text))
                emitter.onComplete()
            }
        } catch (e: Throwable) {
            if (!emitter.isDisposed) emitter.onError(e)
        } finally {
            speechRecognizer.recognizing.removeEventListener(listener)
            microphoneStream.dbListener = null
        }
    }

    override fun stopRecognition() {
        speechRecognizer.stopContinuousRecognitionAsync()
        speechRecognizerRu.stopContinuousRecognitionAsync()
    }

    override fun startRecording() {
        microphoneStream.startRecording()
    }

    override fun stopRecording() {
        microphoneStream.stopRecording()
    }
}

