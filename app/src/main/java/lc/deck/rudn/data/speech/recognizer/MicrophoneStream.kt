package lc.deck.rudn.data.speech.recognizer

import android.media.AudioFormat.*
import android.media.AudioRecord
import android.media.AudioTrack
import android.media.MediaRecorder
import com.microsoft.cognitiveservices.speech.audio.PullAudioInputStreamCallback
import timber.log.Timber
import kotlin.math.abs


class MicrophoneStream : PullAudioInputStreamCallback() {

    companion object {
        private const val SAMPLE_RATE = 16000
    }

    var dbListener: ((volumeLevel: Float) -> Unit)? = null

    private val bufferSize = AudioTrack.getMinBufferSize(
        SAMPLE_RATE,
        CHANNEL_OUT_MONO,
        ENCODING_PCM_16BIT
    )
    private val recorder = AudioRecord(
        MediaRecorder.AudioSource.VOICE_RECOGNITION,
        SAMPLE_RATE,
        CHANNEL_IN_MONO,
        ENCODING_PCM_16BIT,
        bufferSize
    )

    fun startRecording() {
        recorder.startRecording()
    }

    fun stopRecording() {
        try {
            recorder.stop()
        } catch (e: Throwable) {
            Timber.e(e)
        }
    }

    override fun read(bytes: ByteArray): Int {
        try {
            dbListener?.invoke(calculateVolumeLevel(bytes))
            return recorder.read(bytes, 0, bytes.size)
        } catch (e: Throwable) {
            Timber.e(e)
        }
        return 0
    }

    override fun close() {
        try {
            recorder.release()
        } catch (e: Throwable) {
            Timber.e(e)
        }
    }

    private fun calculateVolumeLevel(bytes: ByteArray): Float {
        val decibel = calculateDb(bytes).coerceAtMost(50)
        return decibel / 50f
    }

    //более менее работает
    private fun calculateDb(buf: ByteArray): Int {
        var sum = 0
        for (i in 0 until bufferSize) {
            sum += abs(buf[i].toInt())
        }
        // avg 10-50
        return sum / bufferSize
    }

    //не работает
//    private fun calculateDb(bytes: ByteArray): Int {
//        // Compute the root-mean-squared of the sound buffer and then apply the formula for
//        // computing the decibel level, 20 * log_10(rms). This is an uncalibrated calculation
//        // that assumes no noise in the samples; with 16-bit recording, it can range from
//        // -90 dB to 0 dB.
//        var sum = 0.0
//        for (rawSample in bytes) {
//            val sample = rawSample / 32768.0
//            sum += sample * sample
//        }
//        val rms = sqrt(sum / bytes.size)
//        val db = 20 * log10(rms)
//        return db.toInt()
//    }

    //не работает
//    private fun calculateDb(samples: ByteArray): Int {
//        val MAX_AUDIO_LEVEL = 0.0
//        val MIN_AUDIO_LEVEL = 127.0
//        var offset = 0
//        var rms = 0.0 // root mean square (RMS) amplitude
//        while (offset < samples.size) {
//            var sample: Double = samples[offset].toDouble()
//            sample /= Short.MAX_VALUE.toDouble()
//            rms += sample * sample
//            offset += 2
//        }
//        val sampleCount = samples.size / 2
//        rms = if (sampleCount == 0) 0.0 else sqrt(rms / sampleCount)
//        var db: Double
//        if (rms > 0) {
//            db = 20 * log10(rms)
//            // XXX The audio level is expressed in -dBov.
//            db = -db
//            // Ensure that the calculated audio level is within the range
//            // between MIN_AUDIO_LEVEL and MAX_AUDIO_LEVEL.
//            if (db > MIN_AUDIO_LEVEL) db = MIN_AUDIO_LEVEL else if (db < MAX_AUDIO_LEVEL) db =
//                MAX_AUDIO_LEVEL
//        } else {
//            db = MIN_AUDIO_LEVEL
//        }
//        return db.toInt()
//    }
}