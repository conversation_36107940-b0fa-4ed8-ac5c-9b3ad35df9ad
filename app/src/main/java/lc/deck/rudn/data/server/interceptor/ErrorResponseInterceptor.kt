package lc.deck.rudn.data.server.interceptor

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import lc.deck.rudn.data.server.error.ServerError
import lc.deck.rudn.data.server.error.ServerErrorWithBody
import lc.deck.rudn.entity.BaseResponse
import okhttp3.Interceptor
import okhttp3.MediaType
import okhttp3.Response
import okio.Buffer
import okio.BufferedSource
import okio.GzipSource
import timber.log.Timber
import java.io.EOFException
import java.nio.charset.Charset

class ErrorResponseInterceptor(private val gson: Gson) : Interceptor {

    private val responseType = object : TypeToken<BaseResponse<Any?>>() {}.type

    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())


        val code = response.code()
        if (code in 400..500){
            Timber.e(response.code().toString())
            throw getError(response)
        }

        return response
    }

    private fun getError(response: Response): Exception {
        val code = response.code()
        try {
            val errorResponseRaw = getErrorResponseRaw(response)
            Timber.d("errorResponseRaw: $errorResponseRaw")
            val errorResponse = gson.fromJson<BaseResponse<Any?>>(errorResponseRaw, responseType)
            errorResponse.error?.let {
                return ServerErrorWithBody(it, code)
            }
            return ServerError(code)
        } catch (e: Throwable) {
            return ServerError(code)
        }
    }

    private fun getErrorResponseRaw(response: Response): String? {
        val headers = response.headers()
        val responseBody = response.body()!!
        val source: BufferedSource = responseBody.source()
        val contentLength = responseBody.contentLength()
        source.request(Long.MAX_VALUE) // Buffer the entire body.

        var buffer = source.buffer

        var gzippedLength: Long? = null
        if ("gzip".equals(headers.get("Content-Encoding"), ignoreCase = true)) {
            gzippedLength = buffer.size()
            var gzippedResponseBody: GzipSource? = null
            try {
                gzippedResponseBody = GzipSource(buffer.clone())
                buffer = Buffer()
                buffer.writeAll(gzippedResponseBody)
            } finally {
                gzippedResponseBody?.close()
            }
        }

        var charset = Charset.forName("UTF-8")
        val contentType: MediaType? = responseBody.contentType()
        if (contentType != null) {
            @Suppress("NULLABILITY_MISMATCH_BASED_ON_JAVA_ANNOTATIONS")
            charset = contentType.charset(Charset.forName("UTF-8"))
        }

        if (!isPlaintext(buffer)) {
            return null
        }

        if (contentLength != 0L) {
            return buffer.clone().readString(charset)
        }

        return null
    }

    private fun isPlaintext(buffer: Buffer): Boolean {
        return try {
            val prefix = Buffer()
            val byteCount = if (buffer.size() < 64) buffer.size() else 64
            buffer.copyTo(prefix, 0, byteCount)
            for (i in 0..15) {
                if (prefix.exhausted()) {
                    break
                }
                val codePoint = prefix.readUtf8CodePoint()
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(
                        codePoint
                    )
                ) {
                    return false
                }
            }
            true
        } catch (e: EOFException) {
            false // Truncated UTF-8 sequence.
        }
    }
}