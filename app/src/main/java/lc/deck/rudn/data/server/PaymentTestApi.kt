package lc.deck.rudn.data.server

import io.reactivex.Single
import lc.deck.rudn.entity.BaseResponse
import lc.deck.rudn.entity.enrollee.contract.ReportBugRequestBody
import lc.deck.rudn.entity.enrollee.contract.Status
import lc.deck.rudn.entity.payment.account.AccountReceipt
import lc.deck.rudn.entity.payment.operation.PaymentOperation
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * Created by Seregaryz on 05.04.2022.
 */
interface PaymentTestApi {

    @POST("incorrect-data")
    fun reportBug(@Body requestBody: ReportBugRequestBody): Single<BaseResponse<Status?>>
    @GET("contract/account")
    fun getAccountNumber(
        @Query("contract_guid") guid: String?
    ): Single<BaseResponse<AccountReceipt?>>

    @GET("contract-pay/list")
    fun getOperationByGuid(
        @Query("guid") guid: String?,
    ): Single<BaseResponse<List<PaymentOperation?>?>>
}