package lc.deck.rudn.di

import android.content.Context
import com.microsoft.cognitiveservices.speech.SpeechConfig
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.microsoft.cognitiveservices.speech.audio.AudioOutputStream
import com.microsoft.cognitiveservices.speech.audio.PushAudioOutputStream
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import io.adaptivecards.objectmodel.HostConfig
import io.adaptivecards.objectmodel.ParseContext
import lc.deck.rudn.BuildConfig
import lc.deck.rudn.R
import lc.deck.rudn.data.speech.recognizer.Recognizer
import lc.deck.rudn.data.speech.recognizer.RecognizerImpl
import lc.deck.rudn.data.speech.synthesizer.PushOutputStreamCallback
import lc.deck.rudn.system.VoiceAssistantSettingsKeeper
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class VoiceAssistantModule {

    companion object {

        @Provides
        fun provideSpeechConfig(settings: VoiceAssistantSettingsKeeper): SpeechConfig =
            SpeechConfig.fromSubscription(BuildConfig.SPEECH_SUBSCRIPTION_KEY, "westeurope")
                .apply {
                    speechRecognitionLanguage = "en-US"
                    speechSynthesisVoiceName = "en-US-AriaNeural"
                }

        @Provides
        @Named("ru")
        fun provideSpeechConfigRu(settings: VoiceAssistantSettingsKeeper): SpeechConfig =
            SpeechConfig.fromSubscription(BuildConfig.SPEECH_SUBSCRIPTION_KEY, "westeurope")
                .apply {
                    speechRecognitionLanguage = "ru-RU"
                    speechSynthesisVoiceName = "ru-RU-DariyaNeural"
                }

        @Provides
        @Singleton
        fun providePushOutputStreamCallback(): PushOutputStreamCallback =
            PushOutputStreamCallback()

        @Provides
        @Singleton
        fun providePushAudioOutputStream(
            streamCallback: PushOutputStreamCallback
        ): PushAudioOutputStream = AudioOutputStream.createPushStream(streamCallback)


        @Provides
        @Singleton
        fun provideAudioConfig(stream: PushAudioOutputStream): AudioConfig =
            AudioConfig.fromStreamOutput(stream)

        @Provides
        fun provideSpeechSynthesizer(
            speechConfig: SpeechConfig,
            audioConfig: AudioConfig
        ): SpeechSynthesizer = SpeechSynthesizer(speechConfig, audioConfig)

        @Provides
        @Named("ru")
        fun provideSpeechSynthesizerRu(
            @Named("ru") speechConfig: SpeechConfig,
            audioConfig: AudioConfig
        ): SpeechSynthesizer = SpeechSynthesizer(speechConfig, audioConfig)

        @Provides
        fun provideHostConfig(@ApplicationContext context: Context): HostConfig {
            val hostConfigJson = context.resources.openRawResource(R.raw.adaptive_card_host_config)
                .bufferedReader()
                .use { it.readText() }
            return HostConfig.DeserializeFromString(hostConfigJson)
        }

        @Provides
        fun provideParseContext(): ParseContext = ParseContext()
    }

    @Binds
    abstract fun bindRecognizer(recognizerImpl: RecognizerImpl): Recognizer
}