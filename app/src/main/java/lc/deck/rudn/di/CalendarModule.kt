package lc.deck.rudn.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import lc.deck.rudn.data.calendar.CalendarDataSource
import lc.deck.rudn.utils.addYear
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class CalendarModule {

    @Provides
    @Singleton
    fun provideCalendarDataSource(): CalendarDataSource {
        val date = Date()
        return CalendarDataSource(
            rawStartDate = date.addYear(-1),
            rawEndDate = date.addYear(1)
        )
    }

    @Provides
    @Singleton
    fun provideCalendar(): GregorianCalendar = with(GregorianCalendar()) {
        val year = get(Calendar.YEAR)
        val month = get(Calendar.MONTH)
        val dayOfMonth = get(Calendar.DAY_OF_MONTH)

        GregorianCalendar(year, month, dayOfMonth).apply {
            firstDayOfWeek = Calendar.MONDAY
            minimalDaysInFirstWeek = 1
        }
    }

    @Provides
    @Singleton
    @CurrentDate //todo может не сработать
    fun provideCurrentDate(): Date = with(GregorianCalendar()) {
        val year = get(Calendar.YEAR)
        val month = get(Calendar.MONTH)
        val dayOfMonth = get(Calendar.DAY_OF_MONTH)

        GregorianCalendar(year, month, dayOfMonth).time
    }
}