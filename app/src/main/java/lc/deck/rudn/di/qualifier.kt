package lc.deck.rudn.di

import javax.inject.Qualifier

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CurrentDate

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DayPosition

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RecordFilePath

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class With<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class EventId

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class Role

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class TeacherId

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class WorkProgramId

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class PhotoPath

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class TextToSpeech

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GroupId

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NotificationId

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiDefault

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiKotlinSerializable

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class PersonnelTransactionsOkHttpBuilder

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class PersonnelTransactionsOkHttpClient

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiPersonnelTransactions

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class FirebaseAnalyticsAnnotation
