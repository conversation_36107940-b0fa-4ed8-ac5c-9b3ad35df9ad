package lc.deck.rudn.di

import android.content.Context
import android.os.Build
import com.google.gson.Gson
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.serialization.json.Json
import lc.deck.rudn.BuildConfig
import lc.deck.rudn.data.cache.EventCalendarCache
import lc.deck.rudn.data.server.Api
import lc.deck.rudn.data.server.ApiWithEventCalendarCache
import lc.deck.rudn.data.server.ContractTestApi
import lc.deck.rudn.data.server.DormTestApi
import lc.deck.rudn.data.server.EnrolleeTestApi
import lc.deck.rudn.data.server.PaymentTestApi
import lc.deck.rudn.data.server.interceptor.AuthHeaderInterceptor
import lc.deck.rudn.data.server.interceptor.ErrorResponseInterceptor
import lc.deck.rudn.system.LanguageManager
import lc.deck.rudn.system.SessionKeeper
import lc.deck.rudn.utils.Tls12SocketFactory.Companion.enableTls12
import lc.deck.rudn.utils.setSslContext
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton
import com.chuckerteam.chucker.api.ChuckerInterceptor

@Module
@InstallIn(SingletonComponent::class)
class NetworkModule {

    @Provides
    fun provideOkHttpClientBuilder(
        @ApplicationContext context: Context
    ): OkHttpClient.Builder =
        OkHttpClient.Builder()
            .addInterceptor( ChuckerInterceptor(context))
            .apply {
            connectTimeout(60, TimeUnit.SECONDS)
            readTimeout(60, TimeUnit.SECONDS)
            writeTimeout(60, TimeUnit.SECONDS)
            enableTls12()
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
                setSslContext(context.resources)
            }
            if (BuildConfig.DEBUG) {
                val httpLogger = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
                addNetworkInterceptor(httpLogger)
            }
        }

    @Provides
    @PersonnelTransactionsOkHttpBuilder
    fun provideOkHttpClientBuilderForPersonnelTransactions(
        @ApplicationContext context: Context
    ): OkHttpClient.Builder =
        OkHttpClient.Builder()
            .addInterceptor( ChuckerInterceptor(context))
            .apply {
            connectTimeout(45, TimeUnit.SECONDS)
            readTimeout(45, TimeUnit.SECONDS)
            writeTimeout(45, TimeUnit.SECONDS)
            enableTls12()
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
                setSslContext(context.resources)
            }
            if (BuildConfig.DEBUG) {
                val httpLogger = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
                addNetworkInterceptor(httpLogger)
            }
        }


    @Provides
    @Singleton
    fun provideOkHttpClient(
        okHttpClientBuilder: OkHttpClient.Builder,
        gson: Gson,
        sessionKeeper: SessionKeeper,
        languageManager: LanguageManager
    ): OkHttpClient =
        with(okHttpClientBuilder) {
            addNetworkInterceptor(AuthHeaderInterceptor(sessionKeeper, languageManager))
            addNetworkInterceptor(ErrorResponseInterceptor(gson))
            if (BuildConfig.DEBUG) {
                val httpLogger = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
                addNetworkInterceptor(httpLogger)
            }
            build()
        }

    @Provides
    @PersonnelTransactionsOkHttpClient
    @Singleton
    fun provideOkHttpClientForPersonnelTransactions(
        @PersonnelTransactionsOkHttpBuilder okHttpClientBuilder: OkHttpClient.Builder,
        gson: Gson,
        sessionKeeper: SessionKeeper,
        languageManager: LanguageManager
    ): OkHttpClient =
        with(okHttpClientBuilder) {
            addNetworkInterceptor(AuthHeaderInterceptor(sessionKeeper, languageManager))
            addNetworkInterceptor(ErrorResponseInterceptor(gson))
            if (BuildConfig.DEBUG) {
                val httpLogger = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
                addNetworkInterceptor(httpLogger)
            }
            build()
        }

    @Provides
    @Singleton
    fun provideApi(
        okHttpClient: OkHttpClient,
        gson: Gson,
        eventCalendarCache: EventCalendarCache
    ): Api {
        val api = with(Retrofit.Builder()) {
            addConverterFactory(GsonConverterFactory.create(gson))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl(BuildConfig.BASE_URL)
            build()
        }.create(Api::class.java)
        return ApiWithEventCalendarCache(eventCalendarCache, api)
    }

    @Provides
    @ApiPersonnelTransactions
    @Singleton
    fun provideApiPersonnelTransactions(
        @PersonnelTransactionsOkHttpClient okHttpClient: OkHttpClient,
        gson: Gson,
        eventCalendarCache: EventCalendarCache
    ): Api {
        val api = with(Retrofit.Builder()) {
            addConverterFactory(GsonConverterFactory.create(gson))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl(BuildConfig.BASE_URL)
            build()
        }.create(Api::class.java)
        return ApiWithEventCalendarCache(eventCalendarCache, api)
    }

    @Provides
    @Singleton
    @ApiKotlinSerializable
    fun provideApiKotlinSerializable(
        okHttpClient: OkHttpClient,
        eventCalendarCache: EventCalendarCache
    ): Api {
        val contentType = MediaType.parse("application/json")
        val decoded = Json { ignoreUnknownKeys = true }
        val api = with(Retrofit.Builder()) {
            addConverterFactory(decoded.asConverterFactory(contentType!!))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl(BuildConfig.BASE_URL)
            build()
        }.create(Api::class.java)
        return ApiWithEventCalendarCache(eventCalendarCache, api)
    }

    @Provides
    @Singleton
    fun provideEnrolleTestApi(
        okHttpClient: OkHttpClient,
        gson: Gson
    ) : EnrolleeTestApi {
        val api = with(Retrofit.Builder()) {
            addConverterFactory(GsonConverterFactory.create(gson))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl("https://46b708f0-7276-4738-bfb7-b9918edeca9d.mock.pstmn.io/")
            build()
        }.create(EnrolleeTestApi::class.java)
        return api
    }

    @Provides
    @Singleton
    fun provideContractTestApi(
        okHttpClient: OkHttpClient,
        gson: Gson
    ) : ContractTestApi {
        val api = with(Retrofit.Builder()) {
            addConverterFactory(GsonConverterFactory.create(gson))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl("https://dbd30e2a-30c6-4fdc-92d2-f4f158ca20b0.mock.pstmn.io/")
            build()
        }.create(ContractTestApi::class.java)
        return api
    }

    @Provides
    @Singleton
    fun provideDormTestApi(
        okHttpClient: OkHttpClient,
        gson: Gson
    ) : DormTestApi {
        val api = with(Retrofit.Builder()) {
            addConverterFactory(GsonConverterFactory.create(gson))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl("https://df81f19f-7311-47d9-a442-8a607f3e5401.mock.pstmn.io/")
            build()
        }.create(DormTestApi::class.java)
        return api
    }

    @Provides
    @Singleton
    fun providePaymentTestApi(
        okHttpClient: OkHttpClient,
        gson: Gson
    ) : PaymentTestApi {
        val api = with(Retrofit.Builder()) {
            addConverterFactory(GsonConverterFactory.create(gson))
            addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            client(okHttpClient)
            baseUrl("https://a021fdc8-825b-41b7-b731-ca72484766de.mock.pstmn.io/")
            build()
        }.create(PaymentTestApi::class.java)
        return api
    }
}