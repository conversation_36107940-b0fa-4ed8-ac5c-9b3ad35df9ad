package lc.deck.rudn.di

import android.content.Context
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import lc.deck.rudn.BuildConfig
import lc.deck.rudn.data.file_manager.FileManager
import lc.deck.rudn.data.file_manager.FileManagerImpl
import lc.deck.rudn.data.server.GsonUTCDateAdapter
import lc.deck.rudn.data.speech.adaptive_card.AdaptiveCardDeserializer
import lc.deck.rudn.data.speech.adaptive_card.ContentRaw
import lc.deck.rudn.entity.develop.AppInfo
import lc.deck.rudn.interactors.SessionInteractor
import lc.deck.rudn.system.ErrorHandler
import lc.deck.rudn.system.ResourceManager
import lc.deck.rudn.system.message.SystemMessageNotifier
import lc.deck.rudn.system.schedulers.AppDispatchers
import lc.deck.rudn.system.schedulers.AppSchedulers
import lc.deck.rudn.system.schedulers.DispatcherProvider
import lc.deck.rudn.system.schedulers.SchedulersProvider
import lc.deck.rudn.ui._global.analytics.FirebaseAnalyticsImpl
import lc.deck.rudn.ui._global.common_interface.AnalyticsRepository
import java.util.Date
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AppModule {

    companion object {

        @Provides
        @Singleton
        fun provideAppInfo(@ApplicationContext context: Context): AppInfo = AppInfo(
            BuildConfig.VERSION_NAME,
            BuildConfig.VERSION_CODE,
            context.packageName,
            BuildConfig.BASE_URL
        )

        @Provides
        @Singleton
        fun provideSchedulers(): SchedulersProvider = AppSchedulers()

        @Provides
        @Singleton
        fun provideDispatchers(): DispatcherProvider = AppDispatchers()

        @Provides
        @Singleton
        fun provideSystemMessageNotifier(): SystemMessageNotifier = SystemMessageNotifier()

        @Provides
        @Singleton
        fun provideGson(): Gson = with(GsonBuilder()) {
            serializeNulls()
            registerTypeAdapter(Date::class.java, GsonUTCDateAdapter())
            registerTypeAdapter(ContentRaw::class.java, AdaptiveCardDeserializer())
            create()
        }

        @Provides
        @Singleton
        fun providedErrorHandler(
            systemMessageNotifier: SystemMessageNotifier,
            schedulers: SchedulersProvider,
            resourceManager: ResourceManager,
            sessionInteractor: SessionInteractor
        ): ErrorHandler =
            ErrorHandler(
                systemMessageNotifier,
                schedulers,
                resourceManager,
                sessionInteractor
            )

    }

    @Binds
    abstract fun bindFileManager(fileManagerImpl: FileManagerImpl): FileManager

    @Binds
    @Singleton
    @FirebaseAnalyticsAnnotation
    abstract fun bindFirebaseRepository(firebaseAnalyticsImpl: FirebaseAnalyticsImpl): AnalyticsRepository
}
